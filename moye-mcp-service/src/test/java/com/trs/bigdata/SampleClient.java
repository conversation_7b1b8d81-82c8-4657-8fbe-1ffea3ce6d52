/*
 * Copyright 2024 - 2024 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.trs.bigdata;

import com.trs.bigdata.pojo.FileInfo;
import io.modelcontextprotocol.client.McpClient;
import io.modelcontextprotocol.spec.McpClientTransport;
import io.modelcontextprotocol.spec.McpSchema.CallToolRequest;
import io.modelcontextprotocol.spec.McpSchema.CallToolResult;
import java.time.Duration;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */

public class SampleClient {

    private final McpClientTransport transport;

    public SampleClient(McpClientTransport transport) {
        this.transport = transport;
    }

    public void run() {

//        var client = McpClient.async(this.transport)
//            .requestTimeout(Duration.ofSeconds(120))
//            .loggingConsumer((notification) -> {
//                System.out.println(notification.data());
//                return Mono.empty();
//            })
//            .build();
//
//        client.initialize().block();
//
//        client.ping().block();
//
//
//        // List and demonstrate tools
//        Mono<ListToolsResult> tools = client.listTools();
//        Flux<Tool> streamTools = tools.flatMapMany(result -> Flux.fromIterable(result.tools()));
//        streamTools.doOnNext(tool -> System.out.println(LocalDateTime.now() + ": Available tool: " + tool.name()))
//            .subscribe();
//
//        Mono<CallToolResult> callToolResult = client.callTool(new CallToolRequest("doSearch",
//            Map.of("query", "查询今日除高新分局外各个分局总警情数量，返回分局代码和警情数量")));
//        Flux<Content> contentFlux = callToolResult.flatMapMany(result -> Flux.fromIterable(result.content()));
//        contentFlux.doOnNext(content -> System.out.println(LocalDateTime.now() + " : " + content.toString()))
//            .subscribe();
//
//        while (true) {
//
//        }

        var client = McpClient.sync(this.transport)
            .requestTimeout(Duration.ofSeconds(300))
            .loggingConsumer((notification) -> System.out.println(notification.data())).build();
        client.initialize();
        client.ping();

        // client.setLoggingLevel(LoggingLevel.DEBUG);

        // List and demonstrate tools
//        ListToolsResult toolsList = client.listTools();
//        System.out.println("Available Tools = " + JsonUtils.toJsonString(toolsList));

//        ListResourcesResult resourcesResult = client.listResources();
//        System.out.println("Available Resources = " + JsonUtils.toJsonString(resourcesResult));

        List<FileInfo> fileInfos = List.of(
            new FileInfo("http://192.168.210.120/llmops/appendix/upload/2025/8/25/1廖华15883887687_238283.xls", "1廖华15883887687.xls"),
            new FileInfo("http://192.168.210.120/llmops/appendix/upload/2025/8/25/1陈志刚15883402111_5a0bb6.xls", "1陈志刚15883402111.xls"),
            new FileInfo("http://192.168.210.120/llmops/appendix/upload/2025/8/25/1白远鑫13689658678_deeb16.xls", "1白远鑫13689658678.xls"),
            new FileInfo("http://192.168.210.120/llmops/appendix/upload/2025/8/25/1李鹏程13890251186_36de69.xls","1李鹏程13890251186.xls"),
            new FileInfo("http://192.168.210.120/llmops/appendix/upload/2025/8/25/1黄英13981057776_bc9b1c.xls","1黄英13981057776.xls"),
            new FileInfo("http://192.168.210.120/llmops/appendix/upload/2025/8/25/1邓健15928311872_07e668.xls","1邓健15928311872.xls"));

        CallToolResult getDoSearchResult = client.callTool(new CallToolRequest("话单分析工具",
            Map.of("excelInfos", fileInfos)));
        System.out.println("获取检索: " + getDoSearchResult);

//        CallToolResult getListResult = client.callTool(new CallToolRequest("获取知识库列表",
//            Map.of("appId", 895)));
//        System.out.println("获取检索: " + getListResult);
//
//        CallToolResult getSearchResult = client.callTool(new CallToolRequest("搜索知识",
//            Map.of("appId", 895, "keywords", List.of("拓尔思"))));
//        System.out.println("获取检索: " + getSearchResult);
//
//
//        ReadResourceResult result = client.readResource(new ReadResourceRequest(
//            "/moye/mcp/data-models/280"
//        ));
//        System.out.println("resource result: " + result);
        client.closeGracefully();
    }

}
