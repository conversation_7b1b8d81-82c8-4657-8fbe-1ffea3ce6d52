package com.trs.bigdata.service;

import com.trs.ai.moye.http.starter.HttpClient;
import com.trs.ai.moye.http.starter.HttpRequestEntity;
import com.trs.ai.moye.http.starter.response.HttpResponseEntity;
import com.trs.bigdata.file.FileReader;
import com.trs.bigdata.file.FileReader.FileContent;
import com.trs.bigdata.model.ModelRequest;
import com.trs.bigdata.model.ModelResponse;
import com.trs.bigdata.pojo.CallRecord;
import com.trs.bigdata.pojo.FileInfo;
import com.trs.bigdata.pojo.TraceInfo;
import com.trs.bigdata.properties.LlmModelProperties;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.utils.JsonUtils;
import io.modelcontextprotocol.server.McpSyncServerExchange;
import io.modelcontextprotocol.spec.McpSchema.LoggingLevel;
import io.modelcontextprotocol.spec.McpSchema.LoggingMessageNotification;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.regex.MatchResult;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * excel 话单比对分析工具
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PhoneCallRecordAnalyzer {

    private final HttpClient httpClient;

    private final LlmModelProperties llmModelProperties;

    private final FileReader fileReader;

    /**
     * 分析话单记录
     *
     * @param callId     用户请求id
     * @param excelInfos 用户上传的excel话单文件列表
     * @param exchange   用户会话
     * @return 分析结果
     */
    @Tool(name = "话单分析", description = "根据用户上传的excel话单文件，分析通话记录，输出通联关系分析信息")
    public String analyze(
        @ToolParam(description = "用户请求id") String callId,
        @ToolParam(description = "附件信息：用户上传的话单文件。", required = false) List<FileInfo> excelInfos,
        McpSyncServerExchange exchange) {
        if (CollectionUtils.isEmpty(excelInfos)) {
            return "没有提供任何文件进行分析。";
        }
        sendLog(exchange, new TraceInfo(callId, "开始分析话单", "", false));
        List<CallRecord> allRecords = new ArrayList<>();
        List<String> keyNumbers = new ArrayList<>();
        for (FileInfo fileInfo : excelInfos) {
            try {
                // Correctly handle the file URI
                List<CallRecord> callRecords = parseExcel(fileInfo.getFileUri(), keyNumbers, callId, exchange);
                allRecords.addAll(callRecords);
            } catch (Exception e) {
                String errMessage = "处理文件 " + fileInfo.getFileName() + " 时出错, 停止分析。";
                log.error(errMessage, e);
                sendLog(exchange, new TraceInfo(callId, errMessage, "错误原因：" + e.getMessage(), true));
                return errMessage;
            }
        }

        if (allRecords.isEmpty()) {
            sendLog(exchange,
                new TraceInfo(callId, "未能从文件中解析出任何通话记录。", "未能从文件中解析出任何通话记录，停止分析",
                    true));
            return "未能从文件中解析出任何通话记录，停止分析";
        }

        sendLog(exchange, new TraceInfo(callId,
            "所有文件解析完成，共 " + allRecords.size() + " 条通话记录，下一步将输出话单分析结果。", "", false));
        return generateReport(allRecords, keyNumbers);
    }

    /**
     * 推送日志
     *
     * @param exchange  McpSyncServerExchange
     * @param traceInfo TraceInfo
     */
    public static void sendLog(McpSyncServerExchange exchange, TraceInfo traceInfo) {
        exchange.loggingNotification(
            LoggingMessageNotification.builder().level(LoggingLevel.EMERGENCY).logger("custom-logger")
                .data(JsonUtils.toJsonString(traceInfo)).build());
    }

    private ModelResponse requestLlm(ModelRequest request) {
        try {
            log.info("大模型请求参数：\n{}", JsonUtils.toJsonString(request));
            HttpRequestEntity requestEntity = HttpRequestEntity
                .post(llmModelProperties.getUrl())
                .body(request)
                .build();
            HttpResponseEntity resultEntity = httpClient.doRequestForEntity(requestEntity);
            String result = resultEntity.getBodyAsString();
            log.info("大模型请求结果：\n{}", result);
            return JsonUtils.parseObject(result, ModelResponse.class);
        } catch (Exception e) {
            throw new BizException("请求大模型失败！cause:" + e.getMessage(), e);
        }
    }

    private List<CallRecord> parseExcel(String filePath, List<String> keyNumbers, String callId,
        McpSyncServerExchange exchange)
        throws Exception {
        String fileName = FilenameUtils.getBaseName(filePath);
        log.info("开始解析文件: {}", fileName);
        TraceInfo traceInfo = new TraceInfo(callId, UUID.randomUUID().toString(), "解析文件: " + fileName);
        sendLog(exchange, traceInfo);
        String phoneNumber = Pattern.compile("1[3-9]\\d{9}")
            .matcher(fileName).results()
            .map(MatchResult::group)
            .findFirst().orElse(null);
        if (phoneNumber == null) {
            log.warn("未能从文件名 {} 中提取到本机号码，解析结束", fileName);
            return List.of();
        }

        log.info("从文件名中提取到手机号码: {}", phoneNumber);
        traceInfo.setContent("\n从文件名中提取到手机号码: " + phoneNumber);
        sendLog(exchange, traceInfo);
        keyNumbers.add(phoneNumber);

         /*
           读取Excel文件
          */
        List<CallRecord> records = new ArrayList<>();
        FileContent content = fileReader.getFileContentWithMd5(filePath);
        try (InputStream is = new ByteArrayInputStream(content.content());
            Workbook workbook = filePath.endsWith("xlsx") ? new XSSFWorkbook(is) : new HSSFWorkbook(is)) {
            for (Sheet sheet : workbook) {
                List<CallRecord> sheetRecords = parseSheet(exchange, sheet, traceInfo);
                if (sheetRecords == null) {
                    continue;
                }
                records.addAll(sheetRecords);
            }
        }
        records.forEach(callRecord -> callRecord.setPhoneNumber(phoneNumber));
        return records;
    }

    @Nullable
    private List<CallRecord> parseSheet(McpSyncServerExchange exchange, Sheet sheet, TraceInfo traceInfo) {
        List<CallRecord> sheetRecords = new ArrayList<>();
        log.info("解析工作表: {}", sheet.getSheetName());
        if (sheet.getPhysicalNumberOfRows() < 2) { // Need at least header + 1 data row
            log.warn("工作表 {} 没有足够的行数, 跳过.", sheet.getSheetName());
            traceInfo.setContent(String.format("%n工作表 %s 没有足够的行数, 跳过.", sheet.getSheetName()));
            sendLog(exchange, traceInfo);
            return null;
        }

        // 1. Read header and create column index map
        log.info("解析表头...");
        List<String> headers = parseHeaders(sheet);
        if (headers.isEmpty()) {
            log.warn("工作表 {} 表头解析失败, 跳过.", sheet.getSheetName());
            traceInfo.setContent(String.format("%n工作表 %s 表头解析失败, 跳过.", sheet.getSheetName()));
            sendLog(exchange, traceInfo);
            return null;
        }
        log.info("表头字段: {}", headers);

                /*
                  定位表头
                 */
        Map<String, Integer> headerMap = parseHeaderIndex(headers);
        if (headerMap.isEmpty()) {
            log.warn("工作表 {} 表头映射失败, 跳过.", sheet.getSheetName());
            traceInfo.setContent(String.format("%n工作表 %s 表头映射失败, 跳过.", sheet.getSheetName()));
            sendLog(exchange, traceInfo);
            return null;
        }
        log.info("表头映射: {}", headerMap);

                /*
                   解析行数据
                 */
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            CallRecord callRecord = parseRowData(sheet, i, headerMap);
            if (callRecord != null) {
                sheetRecords.add(callRecord);
            }
        }
        log.info("工作表 {} 解析完成，共 {} 条记录。", sheet.getSheetName(), sheetRecords.size());
        traceInfo.setContent(
            String.format("%n工作表【%s】解析完成，共解析 %d 条通话记录。", sheet.getSheetName(), sheetRecords.size()));
        sendLog(exchange, traceInfo);
        return sheetRecords;
    }

    @NotNull
    private List<String> parseHeaders(Sheet sheet) {
        Row headerRow = sheet.getRow(0);
        List<String> headers = new ArrayList<>();
        for (Cell cell : headerRow) {
            String headerText = getStringCellValue(cell);
            if (!headerText.isEmpty()) {
                headers.add(headerText);
            }
        }
        return headers;
    }

    @Nullable
    private CallRecord parseRowData(Sheet sheet, int i, Map<String, Integer> headerMap) {
        Row row = sheet.getRow(i);
        if (row == null) {
            return null;
        }
        CallRecord callRecord = new CallRecord();
        Cell targetNumberCell = row.getCell(headerMap.get("对方号码"));
        if (targetNumberCell == null) {
            return null;
        }
        String targetNumber = getStringCellValue(targetNumberCell);
        callRecord.setTargetNumber(targetNumber);

        Cell callTimeCell = row.getCell(headerMap.get("通话时间"));
        if (callTimeCell != null) {
            LocalDateTime callTime = getDateTimeCellValue(callTimeCell);
            callRecord.setCallTime(callTime);
        }

        Cell durationCell = row.getCell(headerMap.get("通话时长"));
        if (durationCell != null) {
            Duration duration = parseDuration(getStringCellValue(durationCell));
            callRecord.setDuration(duration);
        }

        Cell typeCell = row.getCell(headerMap.get("通话类型"));
        if (typeCell != null) {
            String type = getStringCellValue(typeCell);
            callRecord.setType(type);
        }

        Cell locationCell = row.getCell(headerMap.get("通话地点"));
        if (locationCell != null) {
            String location = getStringCellValue(locationCell);
            callRecord.setLocation(location);
        }
        return callRecord;
    }

    private Duration parseDuration(String stringCellValue) {
        if (stringCellValue.matches("\\d+")) {
            return Duration.of(Long.parseLong(stringCellValue), ChronoUnit.SECONDS);
        } else if (stringCellValue.matches("\\d+分\\d+秒")) {
            String[] parts = stringCellValue.split("[分秒]");
            long minutes = Long.parseLong(parts[0]);
            long seconds = Long.parseLong(parts[1]);
            return Duration.ofMinutes(minutes).plusSeconds(seconds);
        } else if (stringCellValue.matches("\\d{1,2}:\\d{1,2}:\\d{1,2}")) {
            String[] parts = stringCellValue.split(":");
            long hours = Long.parseLong(parts[0]);
            long minutes = Long.parseLong(parts[1]);
            long seconds = Long.parseLong(parts[2]);
            return Duration.ofHours(hours).plusMinutes(minutes).plusSeconds(seconds);
        } else if (stringCellValue.matches("\\d{1,2}:\\d{1,2}")) {
            String[] parts = stringCellValue.split(":");
            long minutes = Long.parseLong(parts[0]);
            long seconds = Long.parseLong(parts[1]);
            return Duration.ofMinutes(minutes).plusSeconds(seconds);
        } else {
            return Duration.ZERO;
        }
    }

    @NotNull
    private Map<String, Integer> parseHeaderIndex(List<String> headers) {
        Map<String, Integer> headerMap = new HashMap<>();
        String prompt = """
                我有如下一些标题: %s，
                我需要你告诉我以下信息分别位于哪个字段，请用json格式输出，输出格式如下：{"通话类型":xxx,"对方号码"：xxx，"通话时间":xxx,"通话时长":xxx,"通话地点":xxx,"本机号码":xxx}
            """.formatted(JsonUtils.toJsonString(headers));
        ModelRequest request = new ModelRequest();
        request.setStream(false);
        request.addUserMessage(prompt);
        ModelResponse response = requestLlm(request);
        String messageContentWithoutThink = response.getMessageContentWithoutThink();
        JsonUtils.toMap(messageContentWithoutThink, String.class, String.class)
            .forEach((key, value) -> {
                for (int i = 0; i < headers.size(); i++) {
                    if (headers.get(i).equals(value)) {
                        headerMap.put(key, i);
                        break;
                    }
                }
            });
        return headerMap;
    }

    private String generateReport(List<CallRecord> records, List<String> keyNumbers) {
        StringBuilder report = new StringBuilder();
        report.append("话单分析报告\n");
        report.append("===================================\n");
        report.append("总通话记录数: ").append(records.size()).append("\n");

        List<CallRecord> validRecords = records.stream()
            .filter(r -> StringUtils.isNotBlank(r.getPhoneNumber())
                && StringUtils.isNotBlank(r.getTargetNumber()))
            .toList();
        report.append("有效通话记录数 (有主被叫号码): ").append(validRecords.size()).append("\n\n");

        // 1. 通联关系分析
        for (String keyNumber : keyNumbers) {
            List<String> otherKeyNumbers = keyNumbers.stream().filter(num -> !num.equals(keyNumber)).toList();
            List<CallRecord> relationRecords = validRecords.stream()
                .filter(r -> r.getPhoneNumber().equals(keyNumber)
                    && otherKeyNumbers.contains(r.getTargetNumber())).toList();
            if (!relationRecords.isEmpty()) {
                relationRecords.stream().collect(Collectors.groupingBy(CallRecord::getTargetNumber))
                    .forEach((target, recs) -> {
                        long totalDuration = recs.stream()
                            .mapToLong(r -> r.getDuration().getSeconds()).sum();
                        report.append("号码 ").append(keyNumber)
                            .append(" 与 关联号码 ").append(target)
                            .append(" 通话次数: ").append(recs.size())
                            .append(" 次, 总通话时长: ").append(totalDuration)
                            .append(" 秒\n");
                    });
            }
        }

        report.append("===================================\n");
        report.append("报告生成完毕。\n");

        return report.toString();
    }

    private String getStringCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        return switch (cell.getCellType()) {
            case STRING -> cell.getStringCellValue().trim();
            case NUMERIC -> String.valueOf((long) cell.getNumericCellValue()).trim();
            default -> "";
        };
    }

    private LocalDateTime getDateTimeCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        if (cell.getCellType() == CellType.NUMERIC && DateUtil.isCellDateFormatted(cell)) {
            return cell.getDateCellValue().toInstant()
                .atZone(ZoneId.systemDefault()).toLocalDateTime();
        }
        if (cell.getCellType() == CellType.STRING) {
            try {
                // Attempt to parse common date formats
                return LocalDateTime.parse(cell.getStringCellValue(),
                    java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } catch (java.time.format.DateTimeParseException e) {
                // Ignore if parsing fails
            }
        }
        return null;
    }
}
