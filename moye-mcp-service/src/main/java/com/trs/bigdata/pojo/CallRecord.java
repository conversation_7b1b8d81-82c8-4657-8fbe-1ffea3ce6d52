package com.trs.bigdata.pojo;

import java.time.Duration;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * Represents a single phone call record.
 */
@Data
public class CallRecord {

    /**
     * 通话类型
     */
    private String type;
    /**
     * 通话时间
     */
    private LocalDateTime callTime;
    /**
     * 通话时长
     */
    private Duration duration;

    /**
     * 本机号码
     */
    private String phoneNumber;

    /**
     * 对方号码
     */
    private String targetNumber;
    /**
     * 通话地点
     */
    private String location;
}
