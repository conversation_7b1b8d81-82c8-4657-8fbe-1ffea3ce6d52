package com.trs.bigdata.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * 模型返回结果
 *
 * <AUTHOR>
 * @since 2025/4/16 10:30
 */
@Data
public class ModelResponse {

    private String id;

    private String object;

    private String model;

    private List<Choice> choices;

    private String result;

    /**
     * 思考内容
     *
     * @return 思考内容
     */
    public String getThink() {
        String messageContent = getMessageContent();
        if (messageContent != null) {
            int thinkStart = messageContent.indexOf("<think>");
            int thinkEnd = messageContent.indexOf("</think>");
            if (thinkStart != -1 && thinkEnd != -1) {
                return messageContent.substring(thinkStart + 7, thinkEnd);
            }
        }
        return null;
    }

    /**
     * 去掉think内容
     *
     * @return 去掉think内容后的消息内容
     */
    public String getMessageContentWithoutThink() {
        String messageContent = choices.get(0).getMessage().getContent();
        if (messageContent != null) {
            return messageContent
                .replaceAll("(?s)<think>.*</think>", "")
                .replace("```json", "")
                .replace("```", "")
                .replace("\\\"", "\"")
                .trim();
        }
        return null;
    }

    @Data
    private static class Choice {

        private Integer index;

        private Message message;

        private Message delta;
    }

    @Data
    private static class Message {

        private String role;

        private String content;

        @JsonProperty("tool_calls")
        private List<ToolCall> toolCalls = new ArrayList<>();
    }

    /**
     * 工具调用
     */
    @Data
    public static class ToolCall {

        private String id;

        private String type;

        private Function function;
    }

    /**
     * 工具函数
     */
    @Data
    public static class Function {

        private String name;

        private String arguments;
    }

    /**
     * 获取sql语句
     *
     * @return sql语句
     */
    public String getMessageContent() {
        return result;
    }

    /**
     * 获取流式输出结果
     *
     * @return 结果
     */
    public String getDeltaContent() {
        if (choices != null && !choices.isEmpty()) {
            String content = choices.get(0).getDelta().getContent();
            return content == null ? "" : content;
        }
        return "";
    }

}
