package com.trs.bigdata.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.trs.bigdata.constants.McpConstants;
import com.trs.bigdata.properties.LlmModelProperties;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 大模型请求参数
 *
 * <AUTHOR>
 * @since 2025/4/15 15:49
 */
@Data
@NoArgsConstructor
public class ModelRequest {

    /**
     * 生成控制参数
     */
    @JsonProperty("max_tokens")
    private Integer maxTokens = 4096;

    @JsonProperty("top_p")
    private Double topP = 0.85;

    @JsonProperty("stream")
    private Boolean stream = true;

    @JsonProperty("top_k")
    private Integer topK = 20;

    @JsonProperty("temperature")
    private Double temperature = 0.3;

    @JsonProperty("model")
    private String modelName = "拓天";

    /**
     * 消息内容集合
     */
    private List<Message> messages = new ArrayList<>();

    /**
     * 重复抑制参数
     */
    @JsonProperty("repetition_penalty")
    private Double repetitionPenalty = 1.05;


    public ModelRequest(LlmModelProperties properties) {
        this.modelName = properties.getName();
        this.maxTokens = properties.getMaxTokens();
        this.topP = properties.getTopP();
        this.temperature = properties.getTemperature();
        this.topK = properties.getTopK();
        this.stream = true; // 默认开启流式输出
    }

    /**
     * 消息体嵌套类
     */
    @Data
    public static class Message {

        /**
         * 自然语言内容
         */
        private String content;

        /**
         * 角色标识（system/user/assistant）
         */
        private String role;
        /**
         * 工具名称
         */
        private String toolName;
    }

    /**
     * 添加用户消息体方法
     *
     * @param content 消息内容
     */
    public void addUserMessage(String content) {
        addMessage(content, McpConstants.ROLE_USER);
    }

    private void addMessage(String content, String role) {
        Message message = new Message();
        message.setRole(role);
        message.setContent(content);
        this.messages.add(message);
    }

    /**
     * 添加助手消息体方法
     *
     * @param content 消息内容
     */
    public void addAssistantMessage(String content) {
        addMessage(content, McpConstants.ROLE_ASSISTANT);
    }

    /**
     * 添加系统消息体方法
     *
     * @param content 消息内容
     */
    public void addSystemMessage(String content) {
        addMessage(content, McpConstants.ROLE_SYSTEM);
    }

}


