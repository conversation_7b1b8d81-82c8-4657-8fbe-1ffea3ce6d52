package com.trs.ai.moye.data.model.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.trs.ai.moye.data.model.dto.ConnectionStoragePoints;
import com.trs.ai.moye.data.model.dto.StoragePointParams;
import com.trs.ai.moye.data.model.request.CreateTableRequests.CreateTableRequest;
import com.trs.ai.moye.data.model.request.FileImportDataRequest;
import com.trs.ai.moye.data.model.request.FileModelingRequest;
import com.trs.ai.moye.data.model.request.ods.OdsFieldRequest;
import com.trs.ai.moye.data.model.response.CreateTableResponse;
import com.trs.ai.moye.data.model.response.DataModelFileUploadResponse;
import com.trs.ai.moye.data.model.response.FileImportDataResponse;
import com.trs.ai.moye.data.model.response.FileModelingResponse;
import com.trs.ai.moye.data.standard.dao.DataStandardFieldMapper;
import com.trs.ai.moye.minio.starter.properties.MinioProperties;
import com.trs.ai.moye.storageengine.feign.JobFeign;
import com.trs.ai.moye.storageengine.request.SubmitJobRequest;
import com.trs.ai.moye.storageengine.response.RestfulResponse;
import com.trs.ai.moye.storageengine.service.StorageEngineService;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.common.utils.AssertUtils;
import com.trs.moye.base.common.utils.DateTimeUtils;
import com.trs.moye.base.common.utils.DateTimeUtils.Formatter;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.common.utils.PinyinUtil;
import com.trs.moye.base.data.connection.dao.DataConnectionMapper;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.connection.enums.FileChosenType;
import com.trs.moye.base.data.connection.enums.FileExtension;
import com.trs.moye.base.data.execute.DefaultExecuteParams;
import com.trs.moye.base.data.execute.ExecuteMode;
import com.trs.moye.base.data.model.dao.DataModelExecuteConfigMapper;
import com.trs.moye.base.data.model.dao.DataModelFieldMapper;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataModelExecuteConfig;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.data.model.enums.CreateModeEnum;
import com.trs.moye.base.data.model.enums.CreateTableStatus;
import com.trs.moye.base.data.model.enums.DataSaveMode;
import com.trs.moye.base.data.model.enums.ModelExecuteStatus;
import com.trs.moye.base.data.model.field.mapping.ColumnResponse;
import com.trs.moye.base.data.model.field.mapping.FieldMappingResponse;
import com.trs.moye.base.data.model.field.mapping.MoyeFieldResponse;
import com.trs.moye.base.data.source.dao.DataSourceConfigMapper;
import com.trs.moye.base.data.source.setting.file.FileTypeConfig;
import com.trs.moye.base.data.source.setting.file.FtpDataSourceSettings;
import com.trs.moye.base.data.source.setting.file.chosen.AssignFileChosenConfig;
import com.trs.moye.base.data.standard.entity.DataStandardField;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.dao.DataStorageMapper;
import com.trs.moye.base.data.storage.setting.DataStorageSettings;
import io.minio.BucketExistsArgs;
import io.minio.GetObjectArgs;
import io.minio.GetObjectResponse;
import io.minio.MakeBucketArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileUploadException;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件建模服务接口实现类
 */
@Slf4j
@Service
public class FileModelingServiceImpl implements FileModelingService {

    public static final String STRING = "string";
    public static final String N_R_T = "[\\n\\r\\t]";
    @Resource
    private DataModelMapper dataModelMapper;

    @Resource
    private DataStorageMapper dataStorageMapper;

    @Resource
    private DataModelFieldMapper dataModelFieldMapper;

    @Resource
    private DwdModelService dwdModelService;

    @Resource
    private MinioProperties minioProperties;

    @Resource
    private MinioClient minioClient;

    @Resource
    private JobFeign jobFeign;

    @Resource
    private DataConnectionMapper dataConnectionMapper;

    @Resource
    private DataModelExecuteConfigMapper dataModelExecuteConfigMapper;

    @Resource
    private DataStandardFieldMapper dataStandardFieldMapper;

    @Resource
    private DataSourceConfigMapper dataSourceConfigMapper;

    @Resource
    private StorageEngineService storageEngineService;

    @Resource
    private DataModelService dataModelService;

    @PostConstruct
    private void init() throws FileUploadException {
        try {
            String bucket = minioProperties.getBucket().getFileModeling();
            boolean found = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucket).build());
            if (!found) {
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucket).build());
                log.info("创建Bucket: {}", bucket);
            }
        } catch (Exception e) {
            log.error("初始化Minio存储桶失败", e);
            throw new FileUploadException("初始化Minio存储桶失败", e);
        }
    }

    @Override
    public DataModelFileUploadResponse uploadFile(MultipartFile file) throws IOException {
        try {
            String bucketName = minioProperties.getBucket().getFileModeling();
            String originalFilename = file.getOriginalFilename();

            if (originalFilename == null || originalFilename.isEmpty()) {
                throw new IOException("文件名不能为空");
            }

            String extension = FilenameUtils.getExtension(originalFilename);
            String fileName = originalFilename.replace("." + extension, "");
            String now = DateTimeUtils.format(LocalDateTime.now(), Formatter.YYYY_MM_DD_HH_MM_SS_NO_SPLIT);
            String objectName = "t_" + PinyinUtil.toPinyin(fileName) + "_" + now + "." + extension;

            minioClient.putObject(
                PutObjectArgs.builder().bucket(bucketName).object(objectName).stream(
                        file.getInputStream(), file.getSize(), -1)
                    .contentType(file.getContentType())
                    .build());
            log.info("文件上传到Minio成功: {}", objectName);

            // 解析Excel文件中的所有工作表名称
            if (extension.equals("xlsx") || extension.equals("xls")) {
                List<String> sheetNames = parseExcelSheetNames(file.getInputStream(), extension);
                log.info("文件上传成功，工作表名称: {}", sheetNames);
                return DataModelFileUploadResponse.builder()
                    .fileName(objectName)
                    .rawName(originalFilename)
                    .sheetNames(sheetNames)
                    .build();
            } else {
                return DataModelFileUploadResponse.builder()
                    .fileName(objectName)
                    .rawName(originalFilename)
                    .build();
            }
        } catch (Exception e) {
            log.error("上传文件到Minio失败", e);
            throw new IOException("上传文件失败", e);
        }
    }

    /**
     * 解析Excel文件中的所有工作表名称
     *
     * @param fileInputStream Excel文件的输入流
     * @param fileExtension   后缀名
     * @return 工作表名称列表
     * @throws IOException 如果解析Excel文件失败
     */
    private List<String> parseExcelSheetNames(InputStream fileInputStream, String fileExtension) throws IOException {
        try (Workbook workbook = fileExtension.equalsIgnoreCase("xlsx")
            ? new XSSFWorkbook(fileInputStream) : new HSSFWorkbook(fileInputStream)) {
            List<String> sheetNames = new ArrayList<>();
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                sheetNames.add(workbook.getSheetName(i));
            }
            return sheetNames;
        } catch (Exception e) {
            log.error("解析Excel文件失败", e);
            throw new IOException("解析Excel文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public FileModelingResponse fileModelingBatch(FileModelingRequest request) {
        DataModel dataModel = createFileDataModel(request);
        dataModelMapper.insert(dataModel);

        List<DataStorage> storages = createStorages(request, dataModel.getId());
        dataStorageMapper.insert(storages);
        dataModel.setDataStorages(storages);

        DataModelExecuteConfig executeConfig = createExecuteConfig(dataModel);
        dataModelExecuteConfigMapper.insert(executeConfig);
        dataModel.setExecuteConfig(executeConfig);

        DataSourceConfig dataSourceConfig = createDataSourceConfig(dataModel, request.getFileName(),
            request.getSheetName());
        dataSourceConfigMapper.insert(dataSourceConfig);
        dataModel.setDataSource(List.of(dataSourceConfig));

        //如果是选择已有的表作为存储点，需要将已有的表字段同步，以及更改建表状态，修改字段同步字段
        if (request.getStoragePointList().stream().anyMatch(e -> ObjectUtils.isNotEmpty(e.getTableNameList()))) {
            dwdModelService.addStorageInfo(request.getStoragePointList().stream()
                .map(ConnectionStoragePoints::new).toList(), dataModel);
        } else {
            List<DataModelField> fields = createDataModelFields(request, dataModel.getId());
            dataModel.setFields(fields);
            dataModelFieldMapper.insert(fields);
        }
        FileModelingResponse fileModelingResponse = new FileModelingResponse(request, null, true);
        fileModelingResponse.setModelId(dataModel.getId());
        // 建表并立即执行
        storages.forEach(storage -> createTableAndFirstRun(dataModel, storage, request));
        return fileModelingResponse;
    }

    private DataSourceConfig createDataSourceConfig(DataModel dataModel, String fileName, String sheetName) {
        String fileExtension = FilenameUtils.getExtension(fileName);
        FtpDataSourceSettings settings = createFtpDataSourceSettings(fileName, fileExtension, sheetName);

        DataSourceConfig dataSourceConfig = new DataSourceConfig();
        dataSourceConfig.setDataModelId(dataModel.getId());
        dataSourceConfig.setEnName(dataModel.getEnName());
        dataSourceConfig.setZhName(dataModel.getZhName());
        dataSourceConfig.setConnectionId(-2);
        dataSourceConfig.setConnection(dataConnectionMapper.selectById(-2));
        dataSourceConfig.setSettings(settings);
        return dataSourceConfig;
    }

    private static FtpDataSourceSettings createFtpDataSourceSettings(String fileName, String fileExtension,
        String sheetName) {
        FtpDataSourceSettings settings = new FtpDataSourceSettings();
        settings.setConnectionType(ConnectionType.MINIO);
        FileTypeConfig fileTypeConfig = new FileTypeConfig();
        FileExtension fileType = FileExtension.getByExtension(fileExtension);
        fileTypeConfig.setFileType(fileType);
        fileTypeConfig.setSeparator(",");
        fileTypeConfig.setSheetName(sheetName);
        settings.setFileTypeConfig(fileTypeConfig);
        AssignFileChosenConfig fileChosenConfig = new AssignFileChosenConfig();
        fileChosenConfig.setFileName(fileName);
        fileChosenConfig.setFileChosenType(FileChosenType.ASSIGN_FILE);
        settings.setFileChosenConfig(fileChosenConfig);
        return settings;
    }

    private DataModelExecuteConfig createExecuteConfig(DataModel dataModel) {
        DataModelExecuteConfig executeConfig = new DataModelExecuteConfig();
        executeConfig.setDataModelId(dataModel.getId());
        DefaultExecuteParams defaultExecuteParams = new DefaultExecuteParams();
        defaultExecuteParams.setConnectionType(ConnectionType.MINIO);
        executeConfig.setExecuteParams(defaultExecuteParams);
        return executeConfig;
    }

    private List<DataModelField> createDataModelFields(FileModelingRequest request, Integer dataModelId) {
        List<DataModelField> fields = getFieldsFromRequest(request, dataModelId);
        for (DataStandardField builtInField : dataStandardFieldMapper.listBuiltInFields()) {
            DataModelField field = builtInField.toDataModelField();
            field.setProvideService(false);
            field.setDataModelId(dataModelId);
            fields.add(field);
        }
        handleDuplicateFieldNames(fields);
        return fields;
    }

    /**
     * 根据请求获取字段列表
     *
     * @param request     文件建模请求
     * @param dataModelId 数据模型ID
     * @return 字段列表
     */
    private List<DataModelField> getFieldsFromRequest(FileModelingRequest request, Integer dataModelId) {
        List<DataModelField> fields = new ArrayList<>();
        if (Objects.isNull(request.getModelFields())) {
            FieldMappingResponse fieldMappingResponse = getFieldMapping(request.getFileName(), request.getSheetName());
            for (MoyeFieldResponse field : fieldMappingResponse.getModelFields()) {
                DataModelField dataModelField = new DataModelField();
                dataModelField.setDataModelId(dataModelId);
                dataModelField.setEnName(field.getEnName());
                dataModelField.setZhName(field.getZhName());
                dataModelField.setType(field.getType());
                dataModelField.setTypeName(field.getTypeName());
                dataModelField.setPrimaryKey(field.isPrimaryKey());
                fields.add(dataModelField);
            }
        } else {
            for (OdsFieldRequest field : request.getModelFields()) {
                DataModelField dataModelField = new DataModelField();
                dataModelField.setDataModelId(dataModelId);
                dataModelField.setEnName(field.getEnName());
                dataModelField.setZhName(field.getZhName());
                dataModelField.setType(field.getType());
                dataModelField.setTypeName(field.getTypeName());
                dataModelField.setPrimaryKey(field.isPrimaryKey());
                fields.add(dataModelField);
            }
        }
        return fields;
    }

    /**
     * 处理重复的字段名
     *
     * @param fields 字段列表
     */
    private void handleDuplicateFieldNames(List<DataModelField> fields) {
        Set<String> enNames = new HashSet<>();
        Set<String> zhNames = new HashSet<>();
        for (DataModelField field : fields) {
            field.setEnName(getUniqueName(field.getEnName(), enNames));
            field.setZhName(getUniqueName(field.getZhName(), zhNames));
        }
    }

    /**
     * 获取唯一的名称
     *
     * @param name          原始名称
     * @param existingNames 已存在的名称集合
     * @return 唯一名称
     */
    private String getUniqueName(String name, Set<String> existingNames) {
        String uniqueName = name;
        if (existingNames.contains(uniqueName)) {
            int i = 1;
            while (existingNames.contains(uniqueName + i)) {
                i++;
            }
            uniqueName = uniqueName + i;
        }
        existingNames.add(uniqueName);
        return uniqueName;
    }

    private List<DataStorage> createStorages(FileModelingRequest request, Integer dataModelId) {
        List<DataStorage> storages = new ArrayList<>();
        for (StoragePointParams storagePoint : request.getStoragePointList()) {
            DataStorage storage = new DataStorage();
            storage.setDataModelId(dataModelId);
            storage.setEnName(request.getModelEnName());
            storage.setZhName(request.getModelZhName());
            storage.setConnectionId(storagePoint.getConnectionId());
            storage.setCreateTableStatus(CreateTableStatus.NOT);
            storage.setSaveMode(DataSaveMode.APPEND_DATA);
            storages.add(storage);
        }
        return storages;
    }

    private DataModel createFileDataModel(FileModelingRequest request) {
        DataModel dbModel = dataModelMapper.getByEnName(request.getModelEnName());
        AssertUtils.empty(dbModel, "英文名为【%s】的%s已经存在", request.getModelEnName(),
            dbModel == null ? "" : dbModel.getLayer().getLabel());
        DataModel model = new DataModel();
        model.setEnName(request.getModelEnName());
        model.setZhName(request.getModelZhName());
        model.setBusinessCategoryId(request.getBusinessCategoryId());
        model.setCreateMode(CreateModeEnum.FILE_MODELING);
        model.setLayer(request.getModelLayer());
        model.setExecuteStatus(ModelExecuteStatus.STOP);
        model.setIsArranged(true);
        return model;
    }

    @Override
    public FieldMappingResponse getFieldMapping(String fileName, String sheetName) {
        byte[] fileContent = downloadFile(fileName);

        if (fileContent == null || fileContent.length == 0) {
            throw new IllegalArgumentException("文件内容解析失败: " + fileName);
        }

        // 获取文件后缀名
        String fileExtension = FilenameUtils.getExtension(fileName);
        if (fileExtension.isEmpty()) {
            throw new IllegalArgumentException("文件名没有后缀: " + fileName);
        }

        InputStream inputStream = new ByteArrayInputStream(fileContent);

        // 根据文件后缀名解析字段映射
        return switch (fileExtension) {
            case "csv" ->
                // 处理CSV文件
                parseCsvFile(inputStream);
            case "json" ->
                // 处理JSON文件
                parseJsonFile(inputStream);
            case "xlsx", "xls" ->
                // 处理Excel文件
                parseExcel(fileExtension, inputStream, sheetName);
            default -> throw new IllegalArgumentException("不支持的文件类型: " + fileExtension);
        };
    }

    @Override
    public FileImportDataResponse importData(FileImportDataRequest request) {
        String fileName = request.getFileName();
        String fileExtension = FilenameUtils.getExtension(fileName);
        FtpDataSourceSettings settings = createFtpDataSourceSettings(fileName, fileExtension, request.getSheetName());
        DataSourceConfig dataSourceConfig = dataSourceConfigMapper.selectOneByDataModelId(request.getDataModelId());
        dataSourceConfig.setSettings(settings);
        dataSourceConfigMapper.updateById(dataSourceConfig);

        SubmitJobRequest submitJobRequest = new SubmitJobRequest();
        submitJobRequest.setDataModelId(request.getDataModelId());
        submitJobRequest.setExecuteMode(ExecuteMode.IMMEDIATE);
        DefaultExecuteParams defaultExecuteParams = new DefaultExecuteParams();
        defaultExecuteParams.setConnectionType(ConnectionType.MINIO);
        submitJobRequest.setExecuteParams(defaultExecuteParams);
        RestfulResponse restfulResponse = jobFeign.submitJob(submitJobRequest);
        return new FileImportDataResponse(restfulResponse.getData().toString());
    }

    private byte[] downloadFile(String fileName) {
        try {
            String bucketName = minioProperties.getBucket().getFileModeling();
            GetObjectResponse object = minioClient.getObject(
                GetObjectArgs.builder().bucket(bucketName).object(fileName)
                    .build()
            );
            return IOUtils.toByteArray(object);
        } catch (Exception e) {
            log.error("下载文件失败: {}", fileName, e);
            return new byte[0];
        }
    }

    private FieldMappingResponse parseExcel(String fileExtension, InputStream inputStream, String sheetName) {
        try (Workbook workbook = "xlsx".equalsIgnoreCase(fileExtension)
            ? new XSSFWorkbook(inputStream) : new HSSFWorkbook(inputStream)) {
            Sheet sheet = Optional.ofNullable(workbook.getSheet(sheetName))
                .orElse(workbook.getSheetAt(0));
            // 获取表头（第一行）
            Row headerRow = sheet.getRow(0);
            List<ColumnResponse> sourceColumns = new ArrayList<>();
            List<MoyeFieldResponse> modelFields = new ArrayList<>();
            for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                Cell cell = headerRow.getCell(i);
                String header = getCellValueAsString(cell);

                ColumnResponse column = new ColumnResponse();
                column.setEnName(toColumnName(PinyinUtil.toPinyin(header)));
                column.setZhName(header);
                column.setType(STRING); // 默认类型为字符串
                sourceColumns.add(column);

                MoyeFieldResponse field = new MoyeFieldResponse();
                field.setZhName(header);
                field.setEnName(toColumnName(PinyinUtil.toPinyin(header)));
                field.setType(FieldType.STRING);
                field.setTypeName(FieldType.STRING.toString());
                field.setPrimaryKey(false);
                modelFields.add(field);
            }
            return new FieldMappingResponse(sourceColumns, modelFields);
        } catch (Exception e) {
            log.error("解析Excel文件失败", e);
            throw new IllegalArgumentException("解析Excel文件失败: " + e.getMessage(), e);
        }
    }

    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }
        return switch (cell.getCellType()) {
            case STRING -> cell.getStringCellValue().replaceAll(N_R_T, "");
            case NUMERIC -> String.valueOf((long) cell.getNumericCellValue());
            case BOOLEAN -> String.valueOf(cell.getBooleanCellValue());
            default -> "";
        };
    }

    /**
     * 把拼音转换为一个合法的字段名
     *
     * @param pinyinName 已是拼音的名称
     * @return {@link String} 字段名
     * <AUTHOR>
     * @since 2025/6/21 08:10
     **/
    private String toColumnName(String pinyinName) {
        if (pinyinName == null || pinyinName.isEmpty()) {
            log.error("字段的拼音名称不能为空");
            return pinyinName;
        }

        // 如果name是数字开始，则加一个字母前缀
        if (Character.isDigit(pinyinName.charAt(0))) {
            return "c_" + pinyinName;
        }

        if (pinyinName.charAt(0) == '_') {
            // 如果name是下划线开始，则加一个字母前缀
            return "c_" + pinyinName.substring(1);
        }
        return pinyinName;
    }

    private FieldMappingResponse parseCsvFile(InputStream inputStream) {
        List<String> lines = IOUtils.readLines(inputStream, StandardCharsets.UTF_8);
        if (lines.isEmpty()) {
            throw new IllegalArgumentException("CSV文件为空");
        }
        // 解析表头
        String[] headers = lines.get(0).replaceAll(N_R_T, "").split(",");
        // 提取字段信息
        List<ColumnResponse> columns = new ArrayList<>();
        List<MoyeFieldResponse> fields = new ArrayList<>();

        for (String s : headers) {
            ColumnResponse column = new ColumnResponse();
            column.setEnName(toColumnName(PinyinUtil.toPinyin(s)));
            column.setZhName(s);
            column.setType(STRING);
            columns.add(column);

            MoyeFieldResponse field = new MoyeFieldResponse();
            field.setEnName(toColumnName(PinyinUtil.toPinyin(s)));
            field.setZhName(s);
            field.setType(FieldType.STRING);
            field.setTypeName(FieldType.STRING.toString());
            field.setPrimaryKey(false);
            fields.add(field);
        }

        // 解析数据行
        return new FieldMappingResponse(columns, fields);
    }

    private FieldMappingResponse parseJsonFile(InputStream inputStream) {
        try {
            String jsonContent = IOUtils.toString(inputStream, StandardCharsets.UTF_8)
                .replaceAll(N_R_T, "");  // 去除空白字符
            JsonNode rootNode = JsonUtils.getObjectMapper().readTree(jsonContent);
            List<ColumnResponse> columns = new ArrayList<>();
            List<MoyeFieldResponse> fields = new ArrayList<>();
            // 确保根节点是数组且不为空
            if (rootNode instanceof ArrayNode && !rootNode.isEmpty()) {
                // 获取第一个对象的所有字段
                JsonNode firstObject = rootNode.get(0);
                // 遍历第一个对象的所有字段
                firstObject.fieldNames().forEachRemaining(fieldName -> {
                    ColumnResponse column = new ColumnResponse();
                    column.setEnName(toColumnName(PinyinUtil.toPinyin(fieldName)));
                    // 设置中文名称和描述（根据英文名称）
                    column.setZhName(fieldName);
                    column.setType(STRING); // 默认类型为字符串
                    columns.add(column);

                    MoyeFieldResponse field = new MoyeFieldResponse();
                    field.setEnName(toColumnName(PinyinUtil.toPinyin(fieldName)));
                    field.setZhName(fieldName);
                    field.setType(FieldType.STRING);
                    field.setTypeName(FieldType.STRING.toString());
                    field.setPrimaryKey(false);
                    fields.add(field);
                });
            }
            return new FieldMappingResponse(columns, fields);
        } catch (Exception e) {
            log.error("解析JSON文件失败", e);
            throw new IllegalArgumentException("解析JSON文件失败: " + e.getMessage(), e);
        }
    }

    private void createTableAndFirstRun(DataModel dataModel, DataStorage storage, FileModelingRequest request) {
        DataConnection dataConnection = Optional.ofNullable(storage.getConnection())
            .orElse(dataConnectionMapper.selectById(storage.getConnectionId()));
        ConnectionType connectionType = dataConnection.getConnectionType();
        DataStorageSettings settings = DataStorageSettings.getDefault(connectionType);
        if (settings == null) {
            log.warn("文件建模 建表失败，dataModelId:{}, 不支持的存储类型:{}", dataModel.getId(), connectionType);
            return;
        }
        CreateTableRequest tableRequest = new CreateTableRequest();
        tableRequest.setConnectionId(storage.getConnectionId());
        tableRequest.setDataStorageId(storage.getId());
        tableRequest.setSettings(settings);

        CreateTableResponse result = dataModelService.createTable(dataModel, tableRequest);
        // 建表成功的，执行初次调度写数据
        if (result.isSuccess()) {
            try {
                FileImportDataRequest fileImportDataRequest = new FileImportDataRequest();
                fileImportDataRequest.setFileName(request.getFileName());
                fileImportDataRequest.setDataModelId(dataModel.getId());
                fileImportDataRequest.setSheetName(request.getSheetName());
                fileImportDataRequest.setStorageId(storage.getId());
                importData(fileImportDataRequest);
            } catch (Exception e) {
                log.error("文件建模 建表成功，初次调度写数据失败，dataModelId:{}", dataModel.getId(), e);
            }
        }
    }
}
