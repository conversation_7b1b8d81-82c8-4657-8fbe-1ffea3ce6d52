package com.trs.ai.moye.data.model.service.impl;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.trs.ai.moye.data.model.dao.StorageTaskMapper;
import com.trs.ai.moye.data.model.dto.ExecuteParamDTO;
import com.trs.ai.moye.data.model.dto.OdsModelRequestDTO;
import com.trs.ai.moye.data.model.dto.StoragePointParams;
import com.trs.ai.moye.data.model.entity.StorageTask;
import com.trs.ai.moye.data.model.entity.StorageTask.TaskWriteCount;
import com.trs.ai.moye.data.model.entity.StorageTaskTrace;
import com.trs.ai.moye.data.model.enums.SeaTunnelStorageTraceType;
import com.trs.ai.moye.data.model.request.CreateTableRequests.CreateTableRequest;
import com.trs.ai.moye.data.model.request.DispatchDataRequest;
import com.trs.ai.moye.data.model.request.ImmediateExecuteRequest;
import com.trs.ai.moye.data.model.request.ods.OdsModelRequest;
import com.trs.ai.moye.data.model.request.ods.OdsRequest;
import com.trs.ai.moye.data.model.response.AccessNodeDetail;
import com.trs.ai.moye.data.model.response.CreateTableResponse;
import com.trs.ai.moye.data.model.response.DispatchDataResponse;
import com.trs.ai.moye.data.model.response.OdsCreateResponse;
import com.trs.ai.moye.data.model.response.OdsDataSourceResponse;
import com.trs.ai.moye.data.model.response.ProcessFlowResponse;
import com.trs.ai.moye.data.model.service.DataAccessMonitorConfigService;
import com.trs.ai.moye.data.model.service.DataModelService;
import com.trs.ai.moye.data.model.service.OdsModelService;
import com.trs.ai.moye.data.model.task.start.TaskStart;
import com.trs.ai.moye.monitor.dao.DataAccessTraceMapper;
import com.trs.ai.moye.monitor.dao.StorageTaskTraceMapper;
import com.trs.ai.moye.monitor.entity.DataAccessTrace;
import com.trs.ai.moye.storageengine.feign.JobFeign;
import com.trs.ai.moye.storageengine.service.StorageEngineService;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.enums.TaskStatus;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.request.PageParams;
import com.trs.moye.base.common.request.SearchParams;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.common.utils.AssertUtils;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.data.connection.dao.DataConnectionMapper;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.execute.ExecuteParams;
import com.trs.moye.base.data.model.dao.BusinessCategoryMapper;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.data.model.enums.ModelExecuteStatus;
import com.trs.moye.base.data.source.dao.DataSourceConfigMapper;
import com.trs.moye.base.data.source.setting.http.HttpDataSourceSettings;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.dao.DataStorageMapper;
import com.trs.moye.base.data.storage.setting.DataStorageSettings;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 帖源库服务实现
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-27 21:24
 */
@Slf4j
@Service
public class OdsModelServiceImpl implements OdsModelService {

    @Resource
    private DataModelService dataModelService;

    @Resource
    private DataConnectionMapper dataConnectionMapper;

    @Resource
    private BusinessCategoryMapper businessCategoryMapper;

    @Resource
    private DataModelMapper dataModelMapper;

    @Resource
    private DataSourceConfigMapper dataSourceConfigMapper;

    @Resource
    private TaskStart taskStart;

    @Resource
    private StorageTaskMapper storageTaskMapper;

    @Resource
    private StorageEngineService storageEngineService;

    @Resource
    private OdsCreateService odsCreateService;

    @Resource
    private StorageTaskTraceMapper storageTaskTraceMapper;

    @Resource
    private DataAccessTraceMapper dataAccessTraceMapper;

    @Resource
    private DataAccessMonitorConfigService dataAccessMonitorConfigService;

    @Resource
    private DataStorageMapper dataStorageMapper;

    @Resource
    private JobFeign jobFeign;

    public static final String MSG_DATA_MODEL_NOT_EXIST = "主键为【%s】的数据建模不存在";


    /**
     * 创建帖源库：batchCreateOds不能添加事务注解，批量创建只需要回滚创建失败的贴源库，创建成功的不需要回滚； 事务控制由createOds方法控制；
     *
     * @param request 请求参数
     * @return 创建结果
     */
    @Override
    public List<OdsCreateResponse> batchCreateOds(OdsRequest request) {
        verifyParam(request);
        List<OdsCreateResponse> responseList = new ArrayList<>(request.getModelInfoList().size());
        for (OdsModelRequest modelRequest : request.getModelInfoList()) {
            OdsCreateResponse response = new OdsCreateResponse(modelRequest);
            try {
                Integer dataModelId = odsCreateService.createOds(new OdsModelRequestDTO(request, modelRequest));
                createTables(dataModelMapper.selectById(dataModelId));
                response.setSuccess(true);
                response.setModelId(dataModelId);
            } catch (Exception e) {
                log.error("导入贴源库失败,详细原因：", e);
                response.setSuccess(false);
                response.setErrorMsg(e.getMessage());
            }
            responseList.add(response);
        }
        return responseList;
    }

    private void createTables(DataModel dataModel) {
        try {
            List<DataStorage> dataStorageList = dataStorageMapper.selectByDataModelIdWithConnection(dataModel.getId());
            dataStorageList.forEach(storage -> createTableAndFirstRun(dataModel, storage));
        } catch (Exception e) {
            log.error("ODS 建表失败，dataModelId:{}", dataModel.getId(), e);
        }
    }

    private void createTableAndFirstRun(DataModel dataModel, DataStorage storage) {
        ConnectionType connectionType = storage.getConnection().getConnectionType();
        // TODO 暂不支持ck, hive，doris的自动建表
        DataStorageSettings settings = DataStorageSettings.getDefault(connectionType);
       if (settings == null) {
            log.warn("ODS 建表失败，dataModelId:{}, 不支持的存储类型:{}", dataModel.getId(), connectionType);
            return;
        }
        CreateTableRequest tableRequest = new CreateTableRequest();
        tableRequest.setConnectionId(storage.getConnectionId());
        tableRequest.setDataStorageId(storage.getId());
        tableRequest.setSettings(settings);

        CreateTableResponse result = dataModelService.createTable(dataModel, tableRequest);
        // 建表成功的，执行初次调度写数据
        if (result.isSuccess()) {
            try {
                jobFeign.submitJobFirstRun(dataModel.getId());
            } catch (Exception e) {
                log.error("ODS 建表成功，初次调度写数据失败，dataModelId:{}", dataModel.getId(), e);
            }
        }
    }

    private void verifyParam(OdsRequest request) {
        AssertUtils.notEmpty(dataConnectionMapper.selectById(request.getDataSourceConnectionId()),
            "主键为【%s】的数据源不存在", request.getDataSourceConnectionId());
        AssertUtils.notEmpty(businessCategoryMapper.selectById(request.getBusinessCategoryId()),
            "主键为【%s】的业务分类不存在", request.getBusinessCategoryId());
        List<Integer> connectionIdList = request.getStoragePointList().stream().map(StoragePointParams::getConnectionId)
            .toList();
        Map<Integer, DataConnection> storagePointMap = dataConnectionMapper.selectBatchIds(connectionIdList).stream()
            .collect(Collectors.toMap(DataConnection::getId, Function.identity()));
        if (storagePointMap.size() == connectionIdList.size()) {
            return;
        }
        for (Integer storagePointId : connectionIdList) {
            if (!storagePointMap.containsKey(storagePointId)) {
                throw new BizException("主键为【%s】的数据存储不存在", storagePointId);
            }
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void startTask(Integer id, ExecuteParams executeParams) {
        ExecuteParamDTO executeParamDTO = new ExecuteParamDTO(executeParams);
        taskStart.startTask(id, executeParamDTO);
        DataModel dataModel = dataModelMapper.selectById(id);
        //如果是kafka或者MQ, 并且不是要素库 则启用的时候增加一次立即执行
        ModelLayer layer = dataModel.getLayer();
        if (executeParams.isMqType() && !ModelLayer.DWD.equals(layer)) {
            //处理立即执行
            handleImmediateExecution(id, executeParams);
        }
        dataAccessMonitorConfigService.updateXxlJobStatus(id, ModelExecuteStatus.START);
    }

    private void handleImmediateExecution(Integer id, ExecuteParams executeParams) {
        ImmediateExecuteRequest request = new ImmediateExecuteRequest();
        request.setExecuteParams(executeParams);
        boolean executeStatus = dataModelService.immediateExecute(request, id);
        if (!executeStatus) {
            String connectionType = executeParams.getConnectionType().getLabel();
            log.error("建模ID:{},任务类型为:{},任务启动时立即执行失败", id, connectionType);
            throw new BizException("建模ID:" + id + ", 任务类型为: " + connectionType + "任务启动时立即执行失败");
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void stopTask(Integer id) {
        taskStart.stopTask(id);
        dataAccessMonitorConfigService.updateXxlJobStatus(id, ModelExecuteStatus.STOP);
    }

    @Override
    public void pauseTask(Integer id) {
        taskStart.pauseOdsTask(id);
        dataAccessMonitorConfigService.updateXxlJobStatus(id, ModelExecuteStatus.PAUSE);
    }


    @Override
    public HttpDataSourceSettings getApiDetail(Integer dataModelId) {
        DataSourceConfig dataSourceConfig = dataSourceConfigMapper.selectOneByDataModelId(dataModelId);
        if (Objects.isNull(dataSourceConfig)) {
            throw new BizException("建模id为 【%s】 数据源配置不存在", dataModelId);
        }
        return (HttpDataSourceSettings) dataSourceConfig.getSettings();
    }

    @Override
    public OdsDataSourceResponse getOdsDataSource(Integer id) {
        DataModel dataModel = dataModelMapper.selectById(id);
        AssertUtils.notEmpty(dataModel, MSG_DATA_MODEL_NOT_EXIST, id);
        return new OdsDataSourceResponse(dataModel.getFirstDataSource());
    }


    @Override
    public StorageTask updateJobStatus(String storageJobId) {
        return storageEngineService.updateJobStatus(storageJobId);
    }

    @Override
    public void stopJob(String storageJobId) {
        storageEngineService.stopJobSingle(storageJobId);
    }


    @Override
    public List<ProcessFlowResponse> getDispatchFlow(String batchNo) {
        List<StorageTaskTrace> traces = storageTaskTraceMapper.selectByBatchNo(batchNo);
        traces.sort(Comparator.comparing(StorageTaskTrace::getNode));
        StorageTask storageTask = storageTaskMapper.selectByBatchNo(batchNo);
        TaskWriteCount[] writeCountInfo = storageTask.getWriteCountInfo();
        List<DataAccessTrace> dataAccessTraces = dataAccessTraceMapper.selectErrorByBatchNo(batchNo);
        boolean isErrorFlag = Objects.nonNull(dataAccessTraces) && !dataAccessTraces.isEmpty();
        // 将追踪信息转换为响应列表
        List<ProcessFlowResponse> result = traces.stream()
            .map(ProcessFlowResponse::new)
            .toList();
        result.forEach(flowResponse -> {
            if ((containsFailData(flowResponse, writeCountInfo) || isErrorFlag)
                && SeaTunnelStorageTraceType.TASK_EXECUTION.name().equals(flowResponse.getType())) {
                flowResponse.setIsException(TaskStatus.EXCEPTION);
            }
        });
        return result;
    }

    private static boolean containsFailData(ProcessFlowResponse e, TaskWriteCount[] writeCountInfo) {
        return Arrays.stream(writeCountInfo)
            .anyMatch(writeCount -> writeCount.getFailCount() > 0);
    }


    @Override
    public List<AccessNodeDetail> getDispatchNodeDetails(String id) {
        List<AccessNodeDetail> detailList = new ArrayList<>();
        StorageTaskTrace storageTaskTrace = storageTaskTraceMapper.selectById(id);
        if (Objects.isNull(storageTaskTrace) || StringUtils.isBlank(storageTaskTrace.getDetails())) {
            return detailList;
        }
        ObjectNode details = JsonUtils.toJsonObject(storageTaskTrace.getDetails());
        assert details != null;
        details.fields().forEachRemaining(entry -> {
            String key = entry.getKey();
            Object value = entry.getValue();
            detailList.add(new AccessNodeDetail(key, value.toString()));
        });
        return detailList;
    }


    @Override
    public PageResponse<DispatchDataResponse> getAccessErrorData(DispatchDataRequest request) {
        SearchParams searchParams = request.getSearchParams();
        PageParams pageParams = request.getPageParams();
        String storageName = request.getStorageName();
        Boolean isError = request.getIsError();
        Page<DataAccessTrace> pageData = dataAccessTraceMapper.selectPage(request.getBatchNo(), searchParams,
            pageParams.toPage(), isError, storageName);

        List<DispatchDataResponse> responses = pageData.getRecords().stream()
            .map(DispatchDataResponse::from)
            .toList();
        return PageResponse.of(responses, (int) pageData.getCurrent(), pageData.getTotal(), (int) pageData.getSize());

    }


}
