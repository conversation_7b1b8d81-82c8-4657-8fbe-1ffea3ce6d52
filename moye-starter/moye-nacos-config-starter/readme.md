# 1. 说明
moye-nacos-config-starter启动器主要作用是封装nacos配置中心，并对本地增量配置同步更新到nacos远程配置  
对于一个配置项来说，如果远程配置存在，使用远程配置，否则本地配置。  
对于远程配置不存在的配置项，将同步更新到远程配置
# 2. 环境引导配置文件
- 引导配置文件是application-{xxx}.properties配置文件，只配置远程配置中心相关的配置项，例如应用名称、nacos地址、spring.config.import等
- 每个application-{xxx}.properties代表一个环境的配置，例如application-develop.properties代表开发环境，application-test.properties代表测试环境
- 通过spring.profiles.active={xxx}使application-{xxx}.properties生效
- 注意：不要试图重构application-{xxx}.properties文件，例如将公共配置项抽取到application.properties文件中，因为这将破坏nacos加载引导性质配置的逻辑
# 3. 本地配置
- 本地配置文件位置：application-config目录
- 配置文件分为公共配置和应用配置两类；
- 约定：对于区分环境的配置项，只配置生产环境的配置项，例如mysql、kafka地址等配置应该配置svc地址
## 3.1. 公共配置文件
- 被多个应用使用的配置文件，例如kafka.properties、ability-center.properties
- app-local-config目录下非应用配置文件即为公共配置
- 约定：通用配置文件只设置在moye-backend项目中，目的是集中统一管理通用配置；其他项目自身的应用配置，使用公共配置时仅通过spring.config.import导入，不需要开启增量配置合并功能
## 3.2. 应用配置文件
- 应用配置文件就是应用自身的配置文件，例如moye.properties、moye-storage-engine.properties
- 位置是application-config/{spring.application.name}.properties
- 应用配置强制开启配置合并功能
# 4. 开启增量配置合并功能
在spring.config.import导入的配置文件后面添加increment-config-merge=true参数。  
xxl-job开启增量配置合并功能如下：  
spring.config.import[3]=nacos:xxl-job.properties?increment-config-merge=true
服务启动时拿nacos中的xxl-job.properties和本地app-local-config/xxl-job.properties合并  
合并逻辑：本地和nacos都有的配置使用nacos的，各自私有的配置保持原状合并，本地私有的会同步到nacos