package com.trs.ai.moye.nacos.config.starter;

import com.alibaba.cloud.nacos.NacosConfigManager;
import com.alibaba.cloud.nacos.NacosConfigProperties;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.context.properties.bind.BindResult;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MutablePropertySources;
import org.springframework.core.env.PropertiesPropertySource;
import org.springframework.core.env.PropertySource;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.support.EncodedResource;
import org.springframework.core.io.support.PropertiesLoaderUtils;
import org.springframework.util.FileCopyUtils;

/**
 * nacos增量配置合并
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-04 11:12
 */
public class NacosIncrementConfigMerger implements EnvironmentPostProcessor {

    private static final String CONFIG_FILE_TYPE = "properties";
    private static final String PROFILE_ACTIVE_KEY = "spring.profiles.active";
    private static final String LOCAL_CONFIG_DIRECTORY = "application-config";
    private static final String MERGER_ENABLE_KEY = "moye.merger.config.enable";

    @Override
    public synchronized void postProcessEnvironment(ConfigurableEnvironment environment,
        SpringApplication application) {
        String mergerConfigEnable = environment.getProperty(MERGER_ENABLE_KEY);
        // 默认开启，如果要关闭，必须在引导配置文件中添加配置：moye.merger.config.enable=false
        if ("false".equalsIgnoreCase(mergerConfigEnable)) {
            printLog("增量配置合并功能已关闭，不执行任何操作");
            return;
        }
        NacosConfigManager nacosConfigManager = createNacosConfigService(environment);
        for (String mergerConfigFile : parseNeedMergerConfigFiles(environment)) {
            String localFilePath = buildLocalFilePath(mergerConfigFile);
            doMerger(environment, nacosConfigManager, localFilePath, mergerConfigFile);
        }
    }

    private NacosConfigManager createNacosConfigService(ConfigurableEnvironment environment) {
        NacosConfigProperties nacosConfigProperties = getNacosConfigProperties(environment);
        return new NacosConfigManager(nacosConfigProperties);
    }

    private NacosConfigProperties getNacosConfigProperties(ConfigurableEnvironment environment) {
        NacosConfigProperties nacosConfigProps = new NacosConfigProperties();
        nacosConfigProps.setServerAddr(environment.getProperty("spring.cloud.nacos.server-addr"));
        nacosConfigProps.setNamespace(environment.getProperty("spring.cloud.nacos.config.namespace"));
        nacosConfigProps.setUsername(environment.getProperty("spring.cloud.nacos.username"));
        nacosConfigProps.setPassword(environment.getProperty("spring.cloud.nacos.password"));
        printLog("nacos服务地址：%s，nacos名称空间：%s，账号：%s，密码：%s"
            , nacosConfigProps.getServerAddr()
            , nacosConfigProps.getNamespace()
            , nacosConfigProps.getUsername()
            , nacosConfigProps.getPassword());
        return nacosConfigProps;
    }

    private String getActiveProfile(ConfigurableEnvironment environment) {
        String activeProfile = environment.getProperty(PROFILE_ACTIVE_KEY);
        if (activeProfile == null || activeProfile.isEmpty()) {
            return "product";
        }
        // 如果有多个profile，取第一个
        return activeProfile.split(",")[0].trim();
    }

    private List<String> parseNeedMergerConfigFiles(ConfigurableEnvironment environment) {
        BindResult<String[]> bind = Binder.get(environment).bind("spring.config.import", String[].class);
        String[] springConfigImports = bind.orElse(new String[0]);
        List<String> needMergerConfigFiles = new ArrayList<>(springConfigImports.length);
        for (String springConfigImport : springConfigImports) {
            if (!springConfigImport.contains("increment-config-merge=true")) {
                continue;
            }
            // nacos:xxl-job.properties?increment-config-merge=true，取[nacos:]到[?]之间的字符串
            String configFileName = springConfigImport.substring(6, springConfigImport.indexOf('?'));
            needMergerConfigFiles.add(configFileName.substring(0, configFileName.lastIndexOf('.')));
        }
        String applicationConfig = environment.getProperty("spring.application.name");
        if (!needMergerConfigFiles.contains(applicationConfig)) {
            needMergerConfigFiles.add(0, applicationConfig);
        }
        // 构造日志信息
        StringBuilder builder = new StringBuilder("需要合并的配置文件：\n");
        for (int i = 0; i < needMergerConfigFiles.size(); i++) {
            builder.append(i + 1).append(". ").append(needMergerConfigFiles.get(i)).append("\n");
        }
        printLog(builder.toString());
        return needMergerConfigFiles;
    }

    private String buildLocalFilePath(String fileName) {
        return LOCAL_CONFIG_DIRECTORY + "/" + fileName + "." + CONFIG_FILE_TYPE;
    }

    private void doMerger(ConfigurableEnvironment environment, NacosConfigManager nacosConfigManager,
        String localFilePath, String remoteFileName) {
        try {
            printLog("===准备合并本地配置文件【%s】和nacos配置文件【%s】===", localFilePath, remoteFileName);
            printLog("加载本地配置");
            Properties localProps = loadLocalProperties(localFilePath);
            printLog("加载Nacos配置");
            Properties remoteProps = loadRemoteProperties(environment, nacosConfigManager.getNacosConfigProperties(),
                remoteFileName);
            printLog("并合并远程配置");
            Properties mergedProps = mergeWithRemoteConfig(localProps, remoteProps);
            printLog("同步本地特有配置到Nacos");
            boolean haveNewProps = syncLocalUniqueConfigs(localFilePath, localProps, remoteFileName, remoteProps,
                nacosConfigManager);
            if (haveNewProps && isImportThisRemoteFile(environment, remoteFileName)) {
                printLog("取代原来的配置文件");
                replaceOriginalPropertySource(environment, nacosConfigManager, remoteFileName, mergedProps);
                printLog("=============================================", localFilePath, remoteFileName);
            }
        } catch (IOException | NacosException e) {
            throw new ConfigMergerException("Failed to merge configurations", e);
        }
    }

    private void replaceOriginalPropertySource(ConfigurableEnvironment environment,
        NacosConfigManager nacosConfigManager,
        String remoteFileName, Properties mergedProps) {
        String propertySourceName = buildNacosPropertySourceName(nacosConfigManager.getNacosConfigProperties(),
            remoteFileName);
        MutablePropertySources propertySources = environment.getPropertySources();
        // 如果environment环境中已存在对应属性源，则替换，否则在application-{启动的配置文件}配置文件之前添加（优先级高于boot配置文件）
        if (isExistPropertySource(propertySources, propertySourceName)) {
            propertySources.replace(propertySourceName, new PropertiesPropertySource(propertySourceName, mergedProps));
        } else {
            String applicationConfigPropertySourceName = getApplicationConfigPropertySourceName(environment,
                propertySources);
            propertySources.addBefore(applicationConfigPropertySourceName,
                new PropertiesPropertySource(propertySourceName, mergedProps));
        }
    }

    private String getApplicationConfigPropertySourceName(ConfigurableEnvironment environment,
        MutablePropertySources propertySources) {
        String activeProfile = getActiveProfile(environment);
        String applicationConfigFile = "application-" + activeProfile + ".properties";
        String applicationConfigPropertySourceName = null;
        for (PropertySource<?> propertySource : propertySources) {
            if (propertySource.getName().contains(applicationConfigFile)) {
                applicationConfigPropertySourceName = propertySource.getName();
                break;
            }
        }
        if (applicationConfigPropertySourceName == null) {
            throw new ConfigMergerException("找不到%s配置文件对应的PropertySource", applicationConfigFile);
        }
        return applicationConfigPropertySourceName;
    }

    private boolean isExistPropertySource(MutablePropertySources propertySources, String propertySourceName) {
        boolean isExistPropertySource = false;
        for (PropertySource<?> propertySource : propertySources) {
            if (propertySource.getName().equals(propertySourceName)) {
                isExistPropertySource = true;
                break;
            }
        }
        return isExistPropertySource;
    }

    private boolean isImportThisRemoteFile(ConfigurableEnvironment environment, String remoteFileName) {
        String[] importConfigFiles = Binder.get(environment).bind("spring.config.import", String[].class)
            .orElse(new String[0]);
        String remoteImportFileName = "nacos:" + remoteFileName + "." + CONFIG_FILE_TYPE;
        boolean isExistImportFile = false;
        for (String importConfigFile : importConfigFiles) {
            if (importConfigFile.startsWith(remoteImportFileName)) {
                isExistImportFile = true;
                break;
            }
        }
        return isExistImportFile;
    }

    private String buildNacosPropertySourceName(NacosConfigProperties nacosConfigProperties, String remoteFileName) {
        return nacosConfigProperties.getGroup() + "@" + remoteFileName + "." + CONFIG_FILE_TYPE;
    }


    /**
     * 在EnvironmentPostProcessor中使用日志框架打印日志，日志上下文还未被加载，日志不是实时输出的，所以直接在控制台打印日志信息
     *
     * @param format 格式化字符串
     * @param args   日志内容参数
     */
    private void printLog(String format, Object... args) {
        System.out.println(NacosIncrementConfigMerger.class.getName() + ": " + String.format(format, args));
    }

    private Properties loadLocalProperties(String localFilePath) throws IOException {
        EncodedResource encodedResource = new EncodedResource(new ClassPathResource(localFilePath),
            StandardCharsets.UTF_8);
        return PropertiesLoaderUtils.loadProperties(encodedResource);
    }

    private Properties loadRemoteProperties(ConfigurableEnvironment environment,
        NacosConfigProperties nacosConfigProperties, String remoteFileName) {
        String propertySourceName = buildNacosPropertySourceName(nacosConfigProperties, remoteFileName);
        MutablePropertySources propertySources = environment.getPropertySources();
        PropertySource<?> propertySource = propertySources.get(propertySourceName);
        Properties remoteProps = new Properties();
        if (propertySource != null) {
            Object source = propertySource.getSource();
            remoteProps.putAll((Map<?, ?>) source);
        }
        return remoteProps;
    }

    private Properties mergeWithRemoteConfig(Properties localProps, Properties remoteProps) {
        Properties mergedProps = new Properties();
        mergedProps.putAll(localProps);
        mergedProps.putAll(remoteProps);
        return mergedProps;
    }

    private boolean syncLocalUniqueConfigs(String localFilePath, Properties localProps,
        String remoteFileName, Properties remoteProps,
        NacosConfigManager nacosConfigManager) throws NacosException, IOException {
        String dataId = getNacosDataId(remoteFileName);
        // 如果没有远程配置，则直接发布原始内容（如果有的话）
        if (remoteProps.isEmpty()) {
            String localFileContent = loadPropertiesFileAsString(localFilePath);
            publishConfigToNacos(nacosConfigManager, dataId, localFileContent);
            return true;
        }
        List<KeyValue> needSyncToNacosProps = getNeedSyncToNacosProps(localProps, remoteProps);
        if (needSyncToNacosProps.isEmpty()) {
            printLog("没有新增的配置项");
            return false;
        }
        printLog("新增的配置项如下：\n%s", needSyncToNacosProps.stream().map(e -> e.getKey() + "=" + e.getValue())
            .collect(Collectors.joining("\n")));
        String originalContent = getNacosConfigOriginalContent(nacosConfigManager, dataId);
        String newContent = buildNewContent(originalContent, needSyncToNacosProps);
        publishConfigToNacos(nacosConfigManager, dataId, newContent);
        printLog(String.format("已成功将%s项配置追加到Nacos", needSyncToNacosProps.size()));
        return true;
    }

    private String getNacosDataId(String remoteFileName) {
        return remoteFileName + "." + CONFIG_FILE_TYPE;
    }

    /**
     * 加载本地Properties配置文件并返回原始字符串内容
     *
     * @param localFilePath 类路径下的配置文件路径（如："application.properties"）
     * @return 配置文件的原始字符串内容
     * @throws IOException 当文件不存在或读取失败时抛出
     */
    private String loadPropertiesFileAsString(String localFilePath) throws IOException {
        // 1. 创建ClassPathResource（支持classpath:前缀和相对路径）
        ClassPathResource resource = new ClassPathResource(localFilePath);

        // 2. 检查文件是否存在
        if (!resource.exists()) {
            throw new IOException("Properties file not found: " + localFilePath);
        }
        // 3. 读取文件内容（使用try-with-resources确保流关闭）
        try (InputStream inputStream = resource.getInputStream()) {
            // 4. 将输入流转换为字符串（保持原始格式）
            byte[] bytes = FileCopyUtils.copyToByteArray(inputStream);
            return new String(bytes, StandardCharsets.UTF_8);
        }
    }

    private List<KeyValue> getNeedSyncToNacosProps(Properties localProps, Properties remoteProps) {
        List<KeyValue> needSyncToNacosProps = new ArrayList<>();
        localProps.forEach((key, value) -> {
            String keyStr = key.toString();
            if (!remoteProps.containsKey(keyStr)) {
                needSyncToNacosProps.add(new KeyValue(keyStr, value.toString()));
            }
        });
        needSyncToNacosProps.sort(Comparator.comparing(KeyValue::getKey));
        return needSyncToNacosProps;
    }

    private String buildNewContent(String originalContent, List<KeyValue> needSyncNacosProps) {
        StringBuilder newContent = new StringBuilder();
        if (originalContent != null && !originalContent.trim().isEmpty()) {
            newContent.append(originalContent);
            if (!originalContent.endsWith("\n")) {
                newContent.append("\n");
            }
        }
        LocalDateTime now = LocalDateTime.now();
        newContent.append("\n# ").append(now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
            .append("追加如下配置\n");
        needSyncNacosProps.forEach(
            newProp -> newContent.append(newProp.getKey()).append("=").append(newProp.getValue()).append("\n"));
        return newContent.toString();
    }

    private String getNacosConfigOriginalContent(NacosConfigManager nacosConfigManager, String dataId) {
        try {
            NacosConfigProperties nacosConfigProperties = nacosConfigManager.getNacosConfigProperties();
            ConfigService configService = nacosConfigManager.getConfigService();
            return configService.getConfig(dataId, nacosConfigProperties.getGroup(), 10000);
        } catch (NacosException e) {
            throw new ConfigMergerException(e);
        }
    }

    private void publishConfigToNacos(NacosConfigManager nacosConfigManager, String dataId, CharSequence content) {
        try {
            NacosConfigProperties nacosConfigProperties = nacosConfigManager.getNacosConfigProperties();
            ConfigService configService = nacosConfigManager.getConfigService();
            // 8. 更新到Nacos
            configService.publishConfig(dataId,
                nacosConfigProperties.getGroup(),
                content.toString(),
                CONFIG_FILE_TYPE);
        } catch (NacosException e) {
            throw new ConfigMergerException(e);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private static class KeyValue {

        private String key;
        private String value;
    }
}