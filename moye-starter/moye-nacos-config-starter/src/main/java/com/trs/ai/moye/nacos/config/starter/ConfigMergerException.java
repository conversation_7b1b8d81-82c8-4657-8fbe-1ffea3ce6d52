package com.trs.ai.moye.nacos.config.starter;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-04 11:12
 */
public class ConfigMergerException extends RuntimeException{

    public ConfigMergerException() {
    }

    public ConfigMergerException(String message) {
        super(message);
    }

    public ConfigMergerException(String message, Throwable cause) {
        super(message, cause);
    }

    public ConfigMergerException(Throwable cause) {
        super(cause);
    }

    public ConfigMergerException(String format, Object... objects) {
        super(String.format(format, objects));
    }

    public ConfigMergerException(Throwable cause, String message) {
        super(message, cause);
    }

    public ConfigMergerException(Throwable cause, String format, Object... objects) {
        super(String.format(format, objects), cause);
    }
}
