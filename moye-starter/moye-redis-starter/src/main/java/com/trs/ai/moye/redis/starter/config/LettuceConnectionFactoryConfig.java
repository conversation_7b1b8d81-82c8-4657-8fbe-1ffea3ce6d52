package com.trs.ai.moye.redis.starter.config;

import com.trs.ai.moye.redis.starter.properties.RedisProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;

/**
 * Redis配置类，需要将每个应用的database分开，手动配置一下
 */
@Configuration
@Slf4j
public class LettuceConnectionFactoryConfig {


    @Value("${spring.application.name}")
    private String appName;

    /**
     * Redis连接工厂
     *
     * @param redisProperties redis配置属性
     * @return LettuceConnectionFactory
     */
    @Bean
    LettuceConnectionFactory lettuceConnectionFactory(RedisProperties redisProperties) {
        RedisStandaloneConfiguration redisConfig = new RedisStandaloneConfiguration(redisProperties.getHost(),
            redisProperties.getPort());
        redisConfig.setDatabase(redisProperties.getDatabaseByAppName(appName));
        redisConfig.setPassword(redisProperties.getPassword());
        LettuceConnectionFactory lettuceConnectionFactory = new LettuceConnectionFactory(redisConfig);
        // 启动时检测redis连接是否可用
        lettuceConnectionFactory.setEagerInitialization(true);
        return lettuceConnectionFactory;
    }
}
