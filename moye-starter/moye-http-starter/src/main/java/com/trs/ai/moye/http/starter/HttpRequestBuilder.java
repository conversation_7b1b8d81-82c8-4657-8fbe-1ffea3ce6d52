package com.trs.ai.moye.http.starter;

import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.data.source.enums.RequestMethod;
import java.util.Map;
import okhttp3.FormBody;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;

/**
 * Http请求构建器
 */
public class HttpRequestBuilder {

    private HttpRequestBuilder() {
    }

    /**
     * 构建请求
     *
     * @param method 请求方法
     * @param entity 请求实体
     * @return 请求对象
     */
    public static Request buildRequest(RequestMethod method, HttpRequestEntity entity) {
        switch (method) {
            case GET:
                return new Request.Builder()
                    .url(entity.getUrl())
                    .get()
                    .headers(entity.getHeaders())
                    .build();
            case POST:
                return new Request.Builder()
                    .url(entity.getUrl())
                    .post(createRequestBody(entity.getBody(), entity.getFormData()))
                    .headers(entity.getHeaders())
                    .build();
            case PUT:
                return new Request.Builder()
                    .url(entity.getUrl())
                    .put(createRequestBody(entity.getBody(), entity.getFormData()))
                    .headers(entity.getHeaders())
                    .build();
            case DELETE:
                if (entity.getBody() == null && entity.getFormData() == null) {
                    return new Request.Builder()
                        .url(entity.getUrl())
                        .delete()
                        .headers(entity.getHeaders())
                        .build();
                } else {
                    return new Request.Builder()
                        .url(entity.getUrl())
                        .delete(createRequestBody(entity.getBody(), entity.getFormData()))
                        .headers(entity.getHeaders())
                        .build();
                }
            default:
                throw new UnsupportedOperationException("Unsupported request method: " + method);
        }
    }

    /**
     * 创建请求体，post、put，delete请求的请求体，支持json和表单数据
     *
     * @param jsonBody json体
     * @param formData 表单数据
     * @return 请求体
     */
    protected static RequestBody createRequestBody(Object jsonBody, Map<String, Object> formData) {
        RequestBody requestBody;
        if (jsonBody != null) {
            String jsonString = JsonUtils.toJsonString(jsonBody);
            if (jsonBody instanceof String) {
                jsonString = (String) jsonBody;
            }
            requestBody = RequestBody.create(MediaType.parse("application/json"), jsonString);
        } else if (formData != null) {
            requestBody = createFormDataRequestBody(formData);
        } else {
            throw new BizException("请求体不允许为空请求体");
        }
        return requestBody;
    }

    private static RequestBody createFormDataRequestBody(Map<String, Object> formData) {
        FormBody.Builder builder = new FormBody.Builder();
        formData.forEach((k, v) -> {
            if (v == null) {
                return;
            }
            builder.add(k, v.toString());
        });
        return builder.build();
    }
}
