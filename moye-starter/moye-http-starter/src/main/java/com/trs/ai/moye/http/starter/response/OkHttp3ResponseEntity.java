package com.trs.ai.moye.http.starter.response;

import com.trs.moye.base.common.exception.RemoteException;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import okhttp3.Headers;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-11 22:29
 */
public class OkHttp3ResponseEntity implements HttpResponseEntity {

    private final String url;

    private final HttpStatus status;

    private final Headers headers;

    private final byte[] body;

    public OkHttp3ResponseEntity(String url, Response response) {
        this.url = url;
        HttpStatus httpStatus = HttpStatus.resolve(response.code());
        this.status = httpStatus == null ? HttpStatus.INTERNAL_SERVER_ERROR : httpStatus;
        this.headers = response.headers();
        try (ResponseBody responseBody = response.body()) {
            this.body = responseBody == null ? new byte[0] : responseBody.bytes();
        } catch (Exception e) {
            throw new RemoteException(e, "读取响应体内容发生异常");
        }
    }

    @Override
    public String getUrl() {
        return url;
    }

    @Override
    public HttpStatus getStatus() {
        return status;
    }

    @Override
    public String getContentType() {
        return getHeader("Content-Type");
    }

    @Override
    public String getHeader(String name) {
        return headers.get(name);
    }

    @Override
    public String getCookie(String name) {
        List<String> cookieKeyValues = headers.values("Set-Cookie");
        if (ObjectUtils.isEmpty(cookieKeyValues)) {
            return "";
        }
        Pattern pattern = getCookiePattern(name);
        for (String cookieKeyValue : cookieKeyValues) {
            Matcher matcher = pattern.matcher(cookieKeyValue);
            if (matcher.find()) {
                // 捕获组2是cookie的值
                return matcher.group(2);
            }
        }
        return "";
    }

    private Pattern getCookiePattern(String cookieName) {
        return Pattern.compile("(?i)(^|; )\\s*" + cookieName + "\\s*=\\s*([^;]+)");
    }

    @Override
    public byte[] getBody() {
        return body;
    }
}
