package com.trs.ai.moye.http.starter;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.data.source.enums.RequestMethod;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import lombok.Builder;
import lombok.Data;
import okhttp3.Headers;
import okhttp3.Request;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * Http请求实体封装
 */
@Builder
@Data
public class HttpRequestEntity {

    private String url;

    private RequestMethod requestMethod;

    private Object body;

    private Map<String, Object> params;

    private Map<String, Object> headers;

    private Map<String, Object> formData;

    /**
     * Post请求
     *
     * @param url 请求地址
     * @return builder
     */
    public static HttpRequestEntityBuilder post(String url) {
        return builder().requestMethod(RequestMethod.POST).url(url);
    }

    /**
     * Get请求
     *
     * @param url 请求地址
     * @return builder
     */
    public static HttpRequestEntityBuilder get(String url) {
        return builder().requestMethod(RequestMethod.GET).url(url);
    }

    /**
     * Put请求
     *
     * @param url 请求地址
     * @return builder
     */
    public static HttpRequestEntityBuilder put(String url) {
        return builder().requestMethod(RequestMethod.PUT).url(url);
    }

    /**
     * Delete请求
     *
     * @param url 请求地址
     * @return builder
     */
    public static HttpRequestEntityBuilder delete(String url) {
        return builder().requestMethod(RequestMethod.DELETE).url(url);
    }

    /**
     * 构建请求头
     *
     * @return 请求头
     */
    @JsonIgnore
    public Headers getHeaders() {
        Headers.Builder headerBuilder = new Headers.Builder();
        if (Objects.nonNull(headers)) {
            for (Entry<String, Object> entry : headers.entrySet()) {
                if (Objects.nonNull(entry.getValue())) {
                    headerBuilder.add(entry.getKey(), entry.getValue().toString());
                }
            }
        }
        return headerBuilder.build();
    }

    /**
     * 构建请求参数
     *
     * @return 请求参数
     */
    public String getBodyString() {
        return JsonUtils.toJsonString(body);
    }

    /**
     * 构建okhttp3的请求体
     *
     * @return 请求体
     */
    public Request toHttp3Request() {
        return HttpRequestBuilder.buildRequest(requestMethod, this);
    }

    /**
     * 获取url：添加构造查询参数的逻辑
     *
     * @return 有查询参数的url
     */
    public String getUrl() {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        if (Objects.nonNull(params)) {
            params.forEach(builder::queryParam);
        }
        return builder.toUriString();
    }
}
