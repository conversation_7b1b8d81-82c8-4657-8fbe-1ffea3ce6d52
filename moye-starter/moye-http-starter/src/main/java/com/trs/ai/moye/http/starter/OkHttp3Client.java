package com.trs.ai.moye.http.starter;

import com.fasterxml.jackson.core.type.TypeReference;
import com.trs.ai.moye.http.starter.response.HttpResponseEntity;
import com.trs.ai.moye.http.starter.response.OkHttp3ResponseEntity;
import com.trs.moye.base.common.exception.RemoteException;
import java.io.IOException;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import okhttp3.Protocol;
import okhttp3.Response;
import okhttp3.logging.HttpLoggingInterceptor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

/**
 * OkHttp的http客户端实现
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-13 21:58
 */
@Slf4j
@Component
public class OkHttp3Client implements HttpClient {

    private final OkHttpClient okHttpClient;

    public OkHttp3Client() {
        HttpLoggingInterceptor httpLoggingInterceptor = createLoggingInterceptor();
        TrustManager[] trustManagers = createTrustManager();
        SSLSocketFactory sslSocketFactory = createSocketFactory(trustManagers);
        okHttpClient = createHttpClient(httpLoggingInterceptor, trustManagers, sslSocketFactory);
    }

    @NotNull
    private static HttpLoggingInterceptor createLoggingInterceptor() {
        return new HttpLoggingInterceptor(s -> {
            try {
                log.info("OkHttp ----- {}", s);
            } catch (Exception e) {
                log.error("OkHttp ----- {}", s, e);
            }
        }).setLevel(HttpLoggingInterceptor.Level.BODY);
    }

    @Nullable
    private SSLSocketFactory createSocketFactory(TrustManager[] trustManagers) {
        try {
            // Install the all-trusting trust manager
            final SSLContext sslContext = SSLContext.getInstance("SSL");
            sslContext.init(null, trustManagers, new java.security.SecureRandom());
            return sslContext.getSocketFactory();
        } catch (Exception exception) {
            log.error("创建SSLSocketFactory失败", exception);
            return null;
        }
    }

    private TrustManager[] createTrustManager() {
        // Create a trust manager that does not validate certificate chains
        return new TrustManager[]{new X509TrustManager() {
            @Override
            public void checkClientTrusted(java.security.cert.X509Certificate[] chain, String authType) {
            }

            @Override
            public void checkServerTrusted(java.security.cert.X509Certificate[] chain, String authType) {
            }

            @Override
            public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                return new java.security.cert.X509Certificate[]{};
            }
        }};
    }

    private OkHttpClient createHttpClient(HttpLoggingInterceptor httpLoggingInterceptor,
        TrustManager[] trustManagers, SSLSocketFactory sslSocketFactory) {
        return new OkHttpClient.Builder()
            .callTimeout(Duration.of(5, ChronoUnit.MINUTES))
            .addNetworkInterceptor(httpLoggingInterceptor)
            .connectionPool(new ConnectionPool(20, 5, TimeUnit.SECONDS))
            .connectTimeout(5, TimeUnit.MINUTES)
            .readTimeout(5, TimeUnit.MINUTES)
            .writeTimeout(5, TimeUnit.MINUTES)
            .retryOnConnectionFailure(true)
            .hostnameVerifier((s, sslSession) -> Boolean.TRUE)
            .sslSocketFactory(sslSocketFactory, (X509TrustManager) trustManagers[0])
            .protocols(Arrays.asList(Protocol.HTTP_1_1)).build();
    }

    @Override
    public HttpResponseEntity doRequestForEntity(HttpRequestEntity entity) {
        try (Response response = okHttpClient.newCall(entity.toHttp3Request()).execute()) {
            return new OkHttp3ResponseEntity(entity.getUrl(), response);
        } catch (IOException e) {
            String errorMsg = String.format("请求【%s】失败！", entity.getUrl());
            log.error(errorMsg);
            throw new RemoteException(e, errorMsg);
        }
    }

    @Override
    public <T> T doRequest(HttpRequestEntity entity, TypeReference<HttpResult<T>> typeReference) {
        HttpResult<T> httpResult = doRequestForObject(entity, typeReference);
        return httpResult.getData();
    }
}
