package com.trs.ai.moye.http.starter.response;

import com.fasterxml.jackson.core.type.TypeReference;
import com.trs.moye.base.common.exception.RemoteException;
import com.trs.moye.base.common.utils.JsonUtils;
import java.nio.charset.StandardCharsets;
import java.util.function.Function;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-11 22:23
 */
public interface HttpResponseEntity {

    /**
     * url地址
     *
     * @return url地址
     */
    String getUrl();

    /**
     * 获取状态码
     *
     * @return HttpStatus
     */
    HttpStatus getStatus();

    /**
     * 获取响应体内容类型
     *
     * @return 内容类型
     */
    String getContentType();

    /**
     * 获取响应头
     *
     * @param name 头名称
     * @return 头值
     */
    String getHeader(String name);

    /**
     * 获取cookie
     *
     * @param name cookie名称
     * @return cookie值
     */
    String getCookie(String name);

    /**
     * 获取响应体
     *
     * @return 响应体
     */
    byte[] getBody();

    /**
     * 获取响应体字符串
     *
     * @return 响应体字符串
     */
    default String getBodyAsString() {
        if (ObjectUtils.isEmpty(getBody())) {
            return "";
        }
        return new String(getBody(), StandardCharsets.UTF_8);
    }

    /**
     * 转化json响应体
     *
     * @param convertFunction 转化函数
     * @param <T>             返回类型
     * @return T
     */
    default <T> T convertJsonBody(Function<String, T> convertFunction) {
        if (getStatus() != HttpStatus.OK) {
            throw new RemoteException("调用【%s】地址，该地址反馈【%s】异常状态码，异常信息：%s", getUrl()
                , getStatus().value(), getBodyAsString());
        }
        try {
            String jsonBody = getBodyAsString();
            return convertFunction.apply(jsonBody);
        } catch (Exception ex) {
            return null;
        }
    }

    /**
     * 转化为对象
     *
     * @param typeReference 类型引用
     * @param <T>           返回类型
     * @return T
     */
    default <T> T toObject(TypeReference<T> typeReference) {
        return convertJsonBody(json -> JsonUtils.parseObject(json, typeReference));
    }
}
