package com.trs.ai.moye.http.starter;

import com.fasterxml.jackson.core.type.TypeReference;
import com.trs.ai.moye.http.starter.response.HttpResponseEntity;

/**
 * http客户端，发送http请求
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-13 21:13
 */
public interface HttpClient {

    /**
     * 发送http请求
     *
     * @param entity 请求实体
     * @return 响应实体
     */
    HttpResponseEntity doRequestForEntity(HttpRequestEntity entity);

    /**
     * 发送http请求，并返回一个类型明确的对象
     *
     * @param entity        请求实体
     * @param typeReference 返回类型的引用类型
     * @param <T>           返回类型
     * @return 响应实体
     */
    default <T> T doRequestForObject(HttpRequestEntity entity, TypeReference<T> typeReference) {
        HttpResponseEntity responseEntity = doRequestForEntity(entity);
        return responseEntity.toObject(typeReference);
    }

    /**
     * 发送http请求
     *
     * @param entity        请求实体
     * @param <T>           返回类型
     * @param typeReference 返回类型的引用类型
     * @return 返回类型的对象
     */
    <T> T doRequest(HttpRequestEntity entity, TypeReference<HttpResult<T>> typeReference);
}
