package com.trs.moye.storage.engine.db.oracle;

import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.storage.engine.exception.DataStorageEngineException;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;

/**
 * Oracle数据库sql工厂
 */
@Slf4j
public class OracleSqlFactory {

    public static final String SELECT_TABLE_NAME_AND_COMMENT = "SELECT TABLE_NAME as table_name,COMMENTS as table_comment FROM ALL_TAB_COMMENTS WHERE OWNER='%s'";

    // 私有构造函数，防止实例化
    private OracleSqlFactory() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    private static final String SELECT_COLUMNS_SQL_TEMPLATE = "SELECT cols.COLUMN_NAME,CASE WHEN cols.data_type LIKE 'INTERVAL%%' THEN 'INTERVAL' ELSE REGEXP_SUBSTR(cols.data_type,'^[^(]+') END AS TYPE_NAME,cols.data_type || CASE WHEN cols.data_type IN ('VARCHAR','VARCHAR2','CHAR') THEN '(' || cols.data_length || ')' WHEN cols.data_type IN ('NVARCHAR2','NCHAR') THEN '(' || cols.char_length || ')' WHEN cols.data_type IN ('NUMBER') AND cols.data_precision IS NOT NULL AND cols.data_scale IS NOT NULL THEN '(' || cols.data_precision || ', ' || cols.data_scale || ')' WHEN cols.data_type IN ('NUMBER') AND cols.data_precision IS NOT NULL AND cols.data_scale IS NULL THEN '(' || cols.data_precision || ')' WHEN cols.data_type IN ('RAW') THEN '(' || cols.data_length || ')' END AS FULL_TYPE_NAME,cols.data_length AS COLUMN_LENGTH,cols.data_precision AS COLUMN_PRECISION,cols.data_scale AS COLUMN_SCALE,com.comments AS COLUMN_COMMENT,cols.data_default AS DEFAULT_VALUE,CASE cols.nullable WHEN 'N' THEN 'NO' ELSE 'YES' END AS IS_NULLABLE FROM all_tab_columns cols LEFT JOIN all_col_comments com ON cols.table_name=com.table_name AND cols.column_name=com.column_name AND cols.owner=com.owner WHERE cols.owner='%s' AND cols.table_name='%s' ORDER BY cols.column_id";

    /**
     * 创建查询表信息的sql
     *
     * @param schema    模式名
     * @param tableName 表名
     * @return sql
     */
    public static String createGetTableInfoSql(String schema, String tableName) {
        return String.format(SELECT_COLUMNS_SQL_TEMPLATE, schema, tableName);
    }

    /**
     * 创建查询表注释的sql
     *
     * @param schema    模式名
     * @param tableName 表名
     * @return sql
     */
    public static String createTableCommentSql(String schema, String tableName) {
        return String.format("SELECT COMMENTS FROM ALL_TAB_COMMENTS WHERE OWNER='%s' AND TABLE_NAME = '%s'", schema,
            tableName);
    }

    private static String buildColumnSql(DataModelField column, boolean createTableFlag) {
        StringBuilder columnSql = new StringBuilder();
        columnSql.append("\"").append(column.getEnName()).append("\" "); // 字段名加上双引号
        OracleTypeConverter oracleTypeConverter = new OracleTypeConverter();
        String columnType = oracleTypeConverter.reconvert(column).getColumnType();
        columnSql.append(columnType);
        if (!column.isNullable()) {
            validateDefaultValue(column, createTableFlag);
            String defaultValueSql = getDefaultValueSql(column);
            columnSql.append(defaultValueSql).append(" NOT NULL ");
        }
        return columnSql.toString();
    }

    private static String getDefaultValueSql(DataModelField column) {
        if (Objects.isNull(column.getDefaultValue())) {
            return ""; // 如果没有默认值，返回空字符串
        }
        FieldType fieldType = column.getType();
        if (FieldType.DATETIME.equals(fieldType) || FieldType.DATE.equals(fieldType)) {
            return String.format(" DEFAULT TO_DATE('%s', 'YYYY-MM-DD HH24:MI:SS') ", column.getDefaultValue());
        } else {
            return String.format(" DEFAULT '%s' ", column.getDefaultValue());
        }
    }

    private static void validateDefaultValue(DataModelField column, boolean createTableFlag) {
        if (Objects.isNull(column.getDefaultValue()) && !createTableFlag) {
            throw new DataStorageEngineException(
                "ORACLE数据库类型非空字段新增时必须要有默认值! Column :" + column.getEnName() + "默认值为空");
        }
    }

    /**
     * 生成ddl语句
     *
     * @param table  表信息
     * @param schema 模式名
     * @return ddl语句
     */
    public static String ddlSql(DataModel table, String schema) {
        // ORACLE所有的都要加上双引号才能保证不变成大写
        StringBuilder createTableSql = new StringBuilder();
        createTableSql.append("CREATE TABLE ")
            .append("\"").append(schema).append("\".") // 模式名加上双引号
            .append("\"").append(table.getEnName()).append("\"") // 表名加上双引号
            .append(" (\n");
        List<DataModelField> fields = table.getFields();
        List<String> columnSql = fields.stream().map(e -> OracleSqlFactory.buildColumnSql(e, true))
            .toList();
        createTableSql.append(String.join(",\n", columnSql)).append("\n)\n");
        return createTableSql.toString();
    }

    /**
     * 构造添加字段的sql
     *
     * @param schema    模式名
     * @param tableName 表名
     * @param fields    字段信息
     * @return sql
     */
    public static String buildAddColumnSql(String schema, String tableName, List<DataModelField> fields) {
        StringBuilder addColumnSql = new StringBuilder();
        addColumnSql.append("ALTER TABLE ")
            .append("\"").append(schema).append("\".") // 模式名加上双引号
            .append("\"").append(tableName).append("\"") // 表名加上双引号
            .append(" ADD (\n");
        for (int i = 0; i < fields.size(); i++) {
            addColumnSql.append(buildColumnSql(fields.get(i), false));
            if (i < fields.size() - 1) {
                addColumnSql.append(",\n");
            }
        }
        addColumnSql.append("\n)");
        return addColumnSql.toString();
    }

    /**
     * 创建查询所有表名的sql
     *
     * @param schema 模式名
     * @return sql
     */
    public static String getAllTableNamesAndCommentSql(String schema) {
        return String.format(SELECT_TABLE_NAME_AND_COMMENT, schema);
    }

    /**
     * 构造获取最大值的sql
     *
     * @param tableName 表名 (当前表名是已经拼接了schema :  schema.tableName)
     * @param filedName 字段名
     * @return 获取最大值的sql
     */
    public static String buildMaxValueSql(String tableName, String filedName) {
        String sql = "select max("
            + filedName
            + ") as " + "\"maxValue\" from "
            + tableName;
        log.info("Oracle获取最大值sql : {}", sql);
        return sql;
    }
}
