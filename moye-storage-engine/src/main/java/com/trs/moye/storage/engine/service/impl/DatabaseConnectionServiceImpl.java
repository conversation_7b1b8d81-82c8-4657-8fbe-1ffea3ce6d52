package com.trs.moye.storage.engine.service.impl;

import com.trs.moye.base.common.entity.field.DecimalAdvanceConfig;
import com.trs.moye.base.common.entity.field.JsonStructureAdvanceConfig;
import com.trs.moye.base.common.entity.field.VectorAdvanceConfig;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.utils.AssertUtils;
import com.trs.moye.base.common.utils.DateTimeUtils;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.data.connection.dao.AuthCertificateMapper;
import com.trs.moye.base.data.connection.dao.DataConnectionMapper;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.entity.KerberosCertificate;
import com.trs.moye.base.data.connection.entity.params.HybaseConnectionParams;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.explore.ExplorationStatus;
import com.trs.moye.base.data.explore.TableExplorationRequest;
import com.trs.moye.base.data.explore.TableExplorationResult;
import com.trs.moye.base.data.explore.TableExplorationResultEntity;
import com.trs.moye.base.data.indicator.dao.IndicatorFieldMapper;
import com.trs.moye.base.data.indicator.entity.DataModelIndicatorFieldEntity;
import com.trs.moye.base.data.indicator.enums.IndicatorFieldType;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.field.mapping.ColumnResponse;
import com.trs.moye.base.data.model.field.mapping.FieldMappingResponse;
import com.trs.moye.base.data.model.field.mapping.MoyeFieldResponse;
import com.trs.moye.base.data.storage.dao.DataStorageMapper;
import com.trs.moye.base.data.storage.setting.DataStorageSettings;
import com.trs.moye.base.mcp.SearchableField;
import com.trs.moye.base.mcp.TableInfo;
import com.trs.moye.storage.engine.dao.TableExplorationResultMapper;
import com.trs.moye.storage.engine.db.BasicTypeDefine;
import com.trs.moye.storage.engine.db.DatabaseConnection;
import com.trs.moye.storage.engine.db.TypeConverter;
import com.trs.moye.storage.engine.db.hybase.HybaseConnection;
import com.trs.moye.storage.engine.db.nebula.NebulaConnection;
import com.trs.moye.storage.engine.db.nebula.transfer.EntityProcessor;
import com.trs.moye.storage.engine.db.nebula.transfer.NebulaEntityProcessorFactory;
import com.trs.moye.storage.engine.exception.DataStorageEngineException;
import com.trs.moye.storage.engine.pojo.constant.NebulaEntity;
import com.trs.moye.storage.engine.pojo.request.connection.TestConnectionRequest;
import com.trs.moye.storage.engine.pojo.request.nebula.NebulaStorageRequest;
import com.trs.moye.storage.engine.pojo.response.ConnectionTestDetailResponse;
import com.trs.moye.storage.engine.pojo.response.StorageEngineResponse;
import com.trs.moye.storage.engine.pojo.response.alltable.TableResponse;
import com.trs.moye.storage.engine.service.DatabaseConnectionService;
import com.trs.moye.storage.engine.utils.BizUtils;
import com.trs.moye.storage.engine.utils.ConnectionUtils;
import com.vesoft.nebula.client.graph.SessionPool;
import com.vesoft.nebula.client.graph.data.ResultSet;
import com.vesoft.nebula.client.graph.data.ResultSet.Record;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 存储服务
 *
 * <AUTHOR>
 * @since 2021/3/31 13:41
 */
@Slf4j
@Service
public class DatabaseConnectionServiceImpl implements DatabaseConnectionService {

    @Resource
    private DataConnectionMapper dataConnectionMapper;

    @Resource
    private DataModelMapper dataModelMapper;

    @Resource
    private AuthCertificateMapper authCertificateMapper;

    @Resource
    private DataStorageMapper dataStorageMapper;

    @Resource
    private IndicatorFieldMapper indicatorFieldMapper;

    @Resource
    private TableExplorationResultMapper tableExplorationResultMapper;

    @Resource
    NebulaEntityProcessorFactory nebulaEntityProcessorFactory;

    // 线程池大小
    @Value("${nebula.data-transfer.thread-pool-size:20}")
    protected int threadPoolSize = 20;

    protected final ExecutorService executorService = Executors.newFixedThreadPool(threadPoolSize);

    @Override
    public boolean testConnection(TestConnectionRequest request) {
        // 设置证书
        Integer certificateId = request.getCertificateId();
        KerberosCertificate certificate = null;
        if (Objects.nonNull(certificateId)) {
            certificate = authCertificateMapper.selectById(certificateId);
            AssertUtils.notEmpty(certificate, "主键为【%s】的认证证书不存在");
        }
        try (DatabaseConnection connection = ConnectionUtils.getConnection(request.getConnectionParams(),
            certificate)) {
            return connection.testConnection();
        } catch (Exception exception) {
            log.error("测试连接失败", exception);
            return false;
        }
    }

    /**
     * 测试链接
     *
     * @param request 测试链接请求
     * @return 测试链接结果
     */
    @Override
    public ConnectionTestDetailResponse testConnectionWithDetail(TestConnectionRequest request) {
        // 设置证书
        Integer certificateId = request.getCertificateId();
        KerberosCertificate certificate = null;
        if (Objects.nonNull(certificateId)) {
            certificate = authCertificateMapper.selectById(certificateId);
            AssertUtils.notEmpty(certificate, "主键为【%s】的认证证书不存在");
        }
        try (DatabaseConnection connection = ConnectionUtils.getConnection(request.getConnectionParams(),
            certificate)) {
            connection.testConnectionWithException();
            return ConnectionTestDetailResponse.success();
        } catch (Exception exception) {
            log.error("测试连接失败", exception);
            return ConnectionTestDetailResponse.failure(exception);
        }
    }

    @Override
    public StorageEngineResponse createTable(Integer connectionId, Integer dataModelId, Integer dataStorageId,
        DataStorageSettings settings) {
        DataConnection dataConnection = dataConnectionMapper.selectById(connectionId);
        DataModel dataModel = dataModelMapper.selectById(dataModelId);
        //不支持表名中包含空格
        List<DataModelField> fields = getFieldsWithoutSpace(dataModel);
        String storageTableName = dataStorageMapper.selectEnNameById(dataStorageId);
        try (DatabaseConnection connection = ConnectionUtils.getConnection(
            dataConnection.getConnectionParams(),
            dataConnection.getKerberosCertificate())) {
            if (connection.tableExist(storageTableName)) {
                return addFields(connectionId, storageTableName, fields);
            }

            // 如果是指标库，需要使用过滤后的字段进行建表
            if (ModelLayer.INDICATOR.equals(dataModel.getLayer())) {
                // 临时设置过滤后的字段用于建表
                dataModel.setFields(fields);
            }
            return connection.createTable(dataModel, settings);
        } catch (Exception exception) {
            log.error("创建表失败", exception);
            return new StorageEngineResponse(false, "创建表失败:" + exception.getMessage());
        }
    }

    private List<DataModelField> getFieldsWithoutSpace(DataModel dataModel) {
        List<DataModelField> fields = dataModel.getFields();

        // 如果是指标库，需要过滤掉应用指标字段
        if (ModelLayer.INDICATOR.equals(dataModel.getLayer())) {
            fields = filterApplicationFields(dataModel.getId(), fields);
            log.info("指标库建表 - 原始字段数量: {}, 过滤后元数据字段数量: {}",
                dataModel.getFields().size(), fields.size());
        }

        List<DataModelField> invalidFields = fields.stream()
            .filter(field -> field.getEnName() != null && field.getEnName().contains(" "))
            .toList();

        if (!invalidFields.isEmpty()) {
            String invalidNames = invalidFields.stream()
                .map(DataModelField::getEnName)
                .collect(Collectors.joining(", "));
            throw new IllegalArgumentException(
                String.format("字段英文名不支持包含空格，违规字段: %s", invalidNames));
        }
        return fields;
    }

    /**
     * 过滤掉应用指标字段，只保留元数据字段用于指标库建表
     * 该方法与DataModelServiceImpl.getIndicatorMetaFieldIds方法逻辑保持一致
     *
     * @param dataModelId 数据模型ID
     * @param allFields   所有字段
     * @return 过滤后的字段列表（只包含元数据字段）
     */
    private List<DataModelField> filterApplicationFields(Integer dataModelId, List<DataModelField> allFields) {
        if (allFields == null || allFields.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            // 获取所有指标字段配置
            List<DataModelIndicatorFieldEntity> indicatorFieldEntities = indicatorFieldMapper.listByDataModelId(
                dataModelId);

            if (indicatorFieldEntities == null || indicatorFieldEntities.isEmpty()) {
                log.info("指标库建表 - 未找到指标字段配置，返回所有字段, dataModelId: {}", dataModelId);
                return allFields;
            }

            // 获取应用指标字段的ID集合
            Set<Integer> applicationFieldIds = indicatorFieldEntities.stream()
                .filter(entity -> entity.getIndicatorType() == IndicatorFieldType.APPLICATION)
                .map(DataModelIndicatorFieldEntity::getDataModelFieldId)
                .collect(Collectors.toSet());

            // 过滤掉应用指标字段，只保留元数据字段
            List<DataModelField> filteredFields = allFields.stream()
                .filter(field -> !applicationFieldIds.contains(field.getId()))
                .collect(Collectors.toList());

            log.debug("指标库建表字段过滤完成 - 原始字段数: {}, 应用指标字段数: {}, 元数据字段数: {}",
                allFields.size(), applicationFieldIds.size(), filteredFields.size());

            return filteredFields;
        } catch (Exception e) {
            log.error("过滤应用指标字段失败，使用原始字段列表, dataModelId: {}", dataModelId, e);
            return allFields;
        }
    }

    @Override
    public boolean dropTable(Integer connectionId, String tableName) {
        DataConnection dataConnection = dataConnectionMapper.selectById(connectionId);
        try (DatabaseConnection connection = ConnectionUtils.getConnection(
            dataConnection.getConnectionParams(),
            dataConnection.getKerberosCertificate())) {
            return connection.dropTable(tableName);
        } catch (Exception exception) {
            log.error("删除表失败", exception);
            throw new IllegalStateException("删除表失败", exception);
        }
    }

    @Override
    public StorageEngineResponse addFields(Integer connectionId, String tableName, List<DataModelField> fields) {
        DataConnection dataConnection = dataConnectionMapper.selectById(connectionId);
        try (DatabaseConnection connection = ConnectionUtils.getConnection(
            dataConnection.getConnectionParams(),
            dataConnection.getKerberosCertificate())) {
            return connection.addFields(tableName, fields);
        } catch (Exception exception) {
            log.error("新增字段失败", exception);
            throw new IllegalStateException("新增字段失败:" + exception.getMessage(), exception);
        }
    }

    @Override
    public boolean tableExist(Integer connectionId, String tableName) {
        DataConnection dataConnection = dataConnectionMapper.selectById(connectionId);
        try (DatabaseConnection connection = ConnectionUtils.getConnection(
            dataConnection.getConnectionParams(),
            dataConnection.getKerberosCertificate())) {
            return connection.tableExist(tableName);
        } catch (Exception exception) {
            log.error("判断表是否存在失败", exception);
            throw new IllegalStateException("判断表是否存在失败", exception);
        }
    }

    @Override
    public List<TableResponse> getAllTables(Integer connectionId) {
        DataConnection dataConnection = dataConnectionMapper.selectById(connectionId);
        try (DatabaseConnection connection = ConnectionUtils.getConnection(
            dataConnection.getConnectionParams(),
            dataConnection.getKerberosCertificate())) {
            return connection.getAllTables();
        } catch (Exception exception) {
            log.error("获取所有表信息失败", exception);
            throw new IllegalStateException("获取所有表信息失败", exception);
        }
    }

    @Override
    public FieldMappingResponse getAllFields(Integer connectionId, String tableName) {
        DataConnection dataConnection = dataConnectionMapper.selectById(connectionId);
        try (DatabaseConnection connection = ConnectionUtils.getConnection(
            dataConnection.getConnectionParams(),
            dataConnection.getKerberosCertificate())) {
            if (connection instanceof HybaseConnection) {
                String database = ((HybaseConnectionParams) dataConnection.getConnectionParams()).getDatabase();
                tableName = database + "." + tableName;
            }
            List<BasicTypeDefine> fieldDefines = connection.getTableFieldDefines(tableName);
            TypeConverter<BasicTypeDefine> typeConverter = connection.getTypeConverter();
            List<ColumnResponse> sourceColumns = fieldDefines.stream().map(this::convertToColumnResponse).toList();
            List<MoyeFieldResponse> dataModelFields = fieldDefines.stream().map(typeConverter::convert)
                .map(this::convertToMoyeFieldResponse).toList();
            return new FieldMappingResponse(sourceColumns, dataModelFields);
        } catch (Exception exception) {
            log.error("获取表字段信息失败", exception);
            throw new IllegalStateException("获取表字段信息失败", exception);
        }
    }

    private ColumnResponse convertToColumnResponse(BasicTypeDefine field) {
        ColumnResponse response = new ColumnResponse();
        response.setZhName(field.getComment());
        response.setEnName(field.getName());
        response.setType(field.getColumnType());
        if ("dense_vector".equals(field.getColumnType())) {
            VectorAdvanceConfig config = new VectorAdvanceConfig();
            config.setType(FieldType.FLOAT_VECTOR);
            config.setDims(field.getScale());
            response.setAdvanceConfig(config);
        } else if ("decimal".equalsIgnoreCase(field.getColumnType())) {
            DecimalAdvanceConfig config = new DecimalAdvanceConfig();
            config.setType(FieldType.DECIMAL);
            config.setAccuracy(field.getPrecision());
            config.setScale(field.getScale());
            response.setAdvanceConfig(config);
        } else if ("object".equalsIgnoreCase(field.getColumnType()) || "nested".equalsIgnoreCase(
            field.getColumnType())) {
            JsonStructureAdvanceConfig jsonObjectConfig = new JsonStructureAdvanceConfig();
            jsonObjectConfig.setType(FieldType.OBJECT);
            jsonObjectConfig.setJsonStructure(field.getFormat());
            response.setAdvanceConfig(jsonObjectConfig);
        }
        BizUtils.supplementColumnResponse(response);
        return response;
    }

    private MoyeFieldResponse convertToMoyeFieldResponse(DataModelField field) {
        MoyeFieldResponse response = new MoyeFieldResponse();
        // 中文名称为空使用英文名称
        response.setZhName(StringUtils.isEmpty(StringUtils.trim(field.getZhName())) ? field.getEnName()
            : StringUtils.trim(field.getZhName()));
        response.setEnName(field.getEnName());
        response.setType(field.getType());
        response.setAdvanceConfig(field.getAdvanceConfig());
        response.setTypeName(field.getTypeName());
        if (ObjectUtils.isEmpty(response.getTypeName())) {
            response.setTypeName(field.getType().getLabel());
        }
        response.setPrimaryKey(field.isPrimaryKey());
        return response;
    }

    @Override
    public void nebulaStorage(NebulaStorageRequest request) {
        for (Integer connectionId : request.getConnectionId()) {
            DataConnection dataConnection = dataConnectionMapper.selectById(connectionId);
            try (NebulaConnection connection = ConnectionUtils.getConnection(
                dataConnection.getConnectionParams(),
                dataConnection.getKerberosCertificate())) {

                connection.dataStorage(request.getData());
            } catch (Exception exception) {
                log.error("Nebula数据存储失败", exception);
                throw new IllegalStateException("Nebula数据存储失败", exception);
            }
        }

    }

    @Override
    public boolean nebulaDataTransfer(Integer sourceConnectionId, Integer targetConnectionId) {
        DataConnection sourceConnection = dataConnectionMapper.selectById(sourceConnectionId);
        DataConnection targetConnection = dataConnectionMapper.selectById(targetConnectionId);
        validateConnections(sourceConnection, targetConnection);
        try {
            doNebulaTransfer(sourceConnection, targetConnection, NebulaEntity.TAG);
            doNebulaTransfer(sourceConnection, targetConnection, NebulaEntity.EDGE);
        } catch (Exception e) {
            log.error("Nebula数据迁移失败", e);
            return false;
        }
        return true;
    }

    @Override
    public String getDdl(Integer connectionId, String tableName) {
        DataConnection dataConnection = dataConnectionMapper.selectById(connectionId);
        try (DatabaseConnection connection = ConnectionUtils.getConnection(
            dataConnection.getConnectionParams(),
            dataConnection.getKerberosCertificate())) {
            return connection.getDdl(tableName);
        } catch (Exception exception) {
            log.error("获取表DDL失败！连接id：{}，表名：{}", connectionId, tableName, exception);
            throw new IllegalStateException("获取表DDL失败", exception);
        }

    }

    @Override
    public boolean loadDataToStorage(Integer connectionId, TableInfo tableInfo) {
        DataConnection dataConnection = dataConnectionMapper.selectById(connectionId);
        try (DatabaseConnection connection = ConnectionUtils.getConnection(
            dataConnection.getConnectionParams(),
            dataConnection.getKerberosCertificate())) {
            // 先调用添加字段，防止审计字段不存在导致加载失败
            addFields(connectionId, tableInfo.getEnName(),
                tableInfo.getFields().stream().map(SearchableField::toDataModelField).toList());
            return connection.loadDataToStorage(tableInfo);
        } catch (Exception exception) {
            log.error("加载数据到存储引擎失败", exception);
            throw new IllegalStateException("加载数据到存储引擎失败: " + exception.getMessage(), exception);
        }
    }

    @Override
    public TableInfo getTableInfo(Integer connectionId, String tableName) {
        DataConnection dataConnection = dataConnectionMapper.selectById(connectionId);
        try (DatabaseConnection connection = ConnectionUtils.getConnection(
            dataConnection.getConnectionParams(),
            dataConnection.getKerberosCertificate())) {
            DataModel dataModel = connection.getTableInfo(tableName);
            TableInfo tableInfo = new TableInfo(dataModel);
            tableInfo.setConnectionId(connectionId);
            return tableInfo;
        } catch (Exception exception) {
            log.error("获取表信息失败", exception);
            throw new IllegalStateException("获取表信息失败: " + exception.getMessage(), exception);
        }
    }

    @Override
    public TableExplorationResult dataExplore(TableExplorationRequest request) {
        TableExplorationResultEntity existingResult = tableExplorationResultMapper.findByConnectionIdAndTableName(
            request.getConnectionId(), request.getTableName());

        if (existingResult != null) {
            if (ExplorationStatus.COMPLETED.equals(existingResult.getStatus())) {
                return existingResult.getResult();
            } else {
                // 如果任务正在进行中或失败，可以返回当前状态
                return null;
            }
        }

        // 插入一条新的记录，状态为 "processing"
        TableExplorationResultEntity newRecord = new TableExplorationResultEntity();
        newRecord.setConnectionId(request.getConnectionId());
        newRecord.setTableName(request.getTableName());
        newRecord.setStatus(ExplorationStatus.PROCESSING);
        tableExplorationResultMapper.insert(newRecord);

        // 异步执行数据探查
        CompletableFuture.runAsync(() -> {
            try {
                DataConnection dataConnection = dataConnectionMapper.selectById(request.getConnectionId());
                try (DatabaseConnection connection = ConnectionUtils.getConnection(
                    dataConnection.getConnectionParams(),
                    dataConnection.getKerberosCertificate())) {
                    log.info("开始数据探查，连接ID: {}, 表名: {}", request.getConnectionId(), request.getTableName());
                    TableExplorationResult explorationResult = connection.dataExplore(request);
                    log.info("数据探查完成，连接ID: {}, 表名: {}, 探查结果： {}", request.getConnectionId(),
                        request.getTableName(), JsonUtils.toJsonString(explorationResult));
                    newRecord.setStatus(ExplorationStatus.COMPLETED);
                    newRecord.setResult(explorationResult);
                    tableExplorationResultMapper.updateById(newRecord);
                }
            } catch (Exception exception) {
                log.error("数据探查失败", exception);
                // 探查失败，更新记录
                newRecord.setStatus(ExplorationStatus.FAILED);
                newRecord.setResult(new TableExplorationResult(request.getTableName(), new ArrayList<>(),
                    List.of(exception.getMessage())));
                tableExplorationResultMapper.updateById(newRecord);
            }
        }, executorService);

        // 立即返回，告知客户端任务已开始
        return null;
    }

    @Override
    public String getMaxValue(Integer connectionId, String table, String column) {
        DataConnection dataConnection = dataConnectionMapper.selectById(connectionId);
        try (DatabaseConnection connection = ConnectionUtils.getConnection(
            dataConnection.getConnectionParams(),
            dataConnection.getKerberosCertificate())) {
            Object maxValue = connection.getMaxValue(table, column);
            if (maxValue == null) {
                return null;
            }
            if (maxValue instanceof LocalDateTime dateTimeValue) {
                return DateTimeUtils.parse(dateTimeValue);
            } else {
                return maxValue.toString();
            }
        } catch (Exception exception) {
            log.error("获取增量值失败", exception);
            throw new IllegalStateException("获取增量值失败", exception);
        }
    }

    private void doNebulaTransfer(DataConnection sourceConnection,
        DataConnection targetConnection, NebulaEntity entityType) {
        // 获取所有Tag或Edge
        List<Record> records = getAllEntity(sourceConnection, entityType);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        long startTime = System.currentTimeMillis();
        for (Record nebulaRecord : records) {
            // 对每个Tag或Edge进行异步处理
            // 将每个处理任务提交给线程池
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                // 创建新的Nebula连接来对Tag或Edge的数据进行处理
                try (NebulaConnection source = createNebulaConnection(sourceConnection);
                    NebulaConnection target = createNebulaConnection(targetConnection)) {
                    SessionPool sourceSessionPool = source.getSessionPool();
                    SessionPool targetSessionPool = target.getSessionPool();
                    String entityName = nebulaRecord.get("Name").asString();
                    if (!source.tableExist(entityName)) {
                        return;
                    }
                    // 处理Tag或Edge名称，加上反引号
                    entityName = String.format("`%s`", entityName);
                    EntityProcessor processor = nebulaEntityProcessorFactory.getEntityProcessor(entityType);
                    processor.process(entityName, sourceSessionPool, targetSessionPool);
                } catch (Exception e) {
                    log.error("Error while processing record: {}", nebulaRecord, e);
                }
            }, executorService);
            futures.add(future);
        }
        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        long endTime = System.currentTimeMillis();
        log.info("EntityType {} processed in {} ms", entityType, endTime - startTime);
    }

    @NotNull
    private List<Record> getAllEntity(DataConnection sourceConnection, NebulaEntity entityType) {
        List<Record> records = new ArrayList<>();
        try (NebulaConnection source = createNebulaConnection(sourceConnection)) {
            ResultSet resultSet = source.getSessionPool().execute("SHOW " + entityType + "S");
            for (int i = 0; i < resultSet.rowsSize(); i++) {
                records.add(resultSet.rowValues(i));
            }
        } catch (Exception e) {
            throw new BizException("Error occurred during data synchronization", e);
        }
        return records;
    }

    private void validateConnections(DataConnection sourceConnection, DataConnection targetConnection) {
        ConnectionType sourceType = sourceConnection.getConnectionParams().getConnectionType();
        if (!ConnectionType.NEBULA.equals(sourceType)) {
            throw new DataStorageEngineException("不支持数据库连接类型: " + sourceType);
        }
        ConnectionType targetType = targetConnection.getConnectionParams().getConnectionType();
        if (!ConnectionType.NEBULA.equals(targetType)) {
            throw new DataStorageEngineException("不支持数据库连接类型: " + targetType);
        }
    }

    private NebulaConnection createNebulaConnection(DataConnection connection) {
        return ConnectionUtils.getConnection(
            connection.getConnectionParams(), connection.getKerberosCertificate());
    }
}
