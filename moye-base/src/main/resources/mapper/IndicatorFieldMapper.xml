<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.moye.base.data.indicator.dao.IndicatorFieldMapper">

    <!-- 结果集映射 -->
    <resultMap id="BaseResultMap" type="com.trs.moye.base.data.indicator.entity.DataModelIndicatorFieldEntity">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="data_model_id" jdbcType="INTEGER" property="dataModelId"/>
        <result column="data_model_field_id" jdbcType="INTEGER" property="dataModelFieldId"/>
        <result column="original_zh_name" jdbcType="VARCHAR" property="originalZhName"/>
        <result column="config" property="config"
            typeHandler="com.trs.moye.base.common.typehandler.PolymorphismTypeHandler"/>
        <result column="indicator_type" jdbcType="VARCHAR" property="indicatorType"/>
        <result column="is_enable" jdbcType="BIT" property="enable"/>
        <result column="execute_order" jdbcType="INTEGER" property="executeOrder"/>
        <result column="create_by" jdbcType="INTEGER" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="INTEGER" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <delete id="deleteByDataModelId">
        delete from data_model_indicator_field_config
        <where>
            and data_model_id = #{dataModelId,jdbcType=INTEGER}
        </where>
    </delete>

    <select id="listByDataModelId" resultMap="BaseResultMap">
        select *
        from data_model_indicator_field_config
        where data_model_id = #{dataModelId,jdbcType=INTEGER}
    </select>

    <update id="updateByDataModelId">
        update data_model_indicator_field_config
        <set>
            <if test="entity.originalZhName != null">original_zh_name = #{entity.originalZhName,jdbcType=VARCHAR},</if>
            <if test="entity.indicatorType != null">indicator_type = #{entity.indicatorType,jdbcType=VARCHAR},</if>
            <if test="entity.config != null">config =
                #{entity.config,typeHandler=com.trs.moye.base.common.typehandler.PolymorphismTypeHandler},
            </if>
            <if test="entity.executeOrder != null">`execute_order` = #{entity.executeOrder,jdbcType=INTEGER},</if>
            is_enable = #{entity.isEnable,jdbcType=BIT},
            update_by = #{entity.updateBy,jdbcType=INTEGER},
            update_time = #{entity.updateTime,jdbcType=TIMESTAMP}
        </set>
        where data_model_field_id = #{entity.dataModelFieldId,jdbcType=INTEGER}
    </update>

    <delete id="deleteByDataModelFieldIds">
        delete from data_model_indicator_field_config
        where data_model_field_id in
        <foreach collection="dataModelFields" item="id" open="(" separator="," close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </delete>

</mapper>
