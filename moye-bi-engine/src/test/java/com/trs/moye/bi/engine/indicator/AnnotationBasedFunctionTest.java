package com.trs.moye.bi.engine.indicator;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.trs.moye.bi.engine.indicator.entity.CalculationFunctionDefinition;
import com.trs.moye.bi.engine.indicator.entity.CalculationParameter;
import com.trs.moye.bi.engine.indicator.function.CalculationFunctionRegistry;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

/**
 * 注解基础功能测试类
 * <p>
 * 测试注解系统的基础功能，包括：
 * <ol>
 *   <li>注解扫描和函数定义生成</li>
 *   <li>参数定义的正确性</li>
 *   <li>函数分类和统计</li>
 *   <li>基本的函数查找功能</li>
 * </ol>
 * </p>
 *
 * <AUTHOR>
 * @since 2025/07/29
 */
@DisplayName("注解基础功能测试")
class AnnotationBasedFunctionTest {

    /**
     * 测试前的初始化
     * <p>
     * 确保函数注册表在测试执行前已正确初始化， 如果注册表为空则重新加载函数定义。
     * </p>
     */
    @BeforeEach
    void setUp() {
        // 确保函数注册表已初始化
        CalculationFunctionRegistry.ensureInitialized();
    }

    /**
     * 测试注解扫描功能
     */
    @Test
    @DisplayName("测试注解扫描功能")
    void testAnnotationScanning() {
        // 获取所有函数
        List<String> allFunctions = CalculationFunctionRegistry.getAllFunctions();

        // 验证基本函数是否被扫描到
        assertNotNull(allFunctions, "函数列表不应为空");
        assertFalse(allFunctions.isEmpty(), "应该扫描到至少一个函数");

        // 验证核心函数是否存在
        assertTrue(allFunctions.contains("add"), "应该包含 add 函数");
        assertTrue(allFunctions.contains("subtract"), "应该包含 subtract 函数");
        assertTrue(allFunctions.contains("multiply"), "应该包含 multiply 函数");
        assertTrue(allFunctions.contains("divide"), "应该包含 divide 函数");
        assertTrue(allFunctions.contains("getFieldData"), "应该包含 getFieldData 函数");
        assertTrue(allFunctions.contains("getTrendData"), "应该包含 getTrendData 函数");
    }

    /**
     * 测试函数定义的完整性
     * <p>
     * 验证指定函数的定义是否完整，包括基本属性（名称、描述、分类、执行器） 和参数定义（名称、描述、类型、legacyIndex）的完整性检查。
     * </p>
     *
     * @param functionName 要测试的函数名称，包括核心的算术运算和数据分析函数
     */
    @ParameterizedTest
    @DisplayName("测试函数定义的完整性")
    @ValueSource(strings = {"add", "subtract", "multiply", "divide", "getFieldData", "getTrendData"})
    void testFunctionDefinitionCompleteness(String functionName) {
        Optional<CalculationFunctionDefinition> definitionOpt =
            CalculationFunctionRegistry.getDefinition(functionName);

        assertTrue(definitionOpt.isPresent(), "函数定义应该存在: " + functionName);

        CalculationFunctionDefinition definition = definitionOpt.get();

        // 验证基本属性
        assertEquals(functionName, definition.getFunctionName(), "函数名称应该匹配");
        assertNotNull(definition.getDescription(), "函数描述不应为空");
        assertFalse(definition.getDescription().trim().isEmpty(), "函数描述不应为空字符串");
        assertNotNull(definition.getCategory(), "函数分类不应为空");
        assertNotNull(definition.getExecutor(), "函数执行器不应为空");

        // 验证参数定义
        List<CalculationParameter> parameters = definition.getParameters();
        assertNotNull(parameters, "参数列表不应为空");

        for (CalculationParameter param : parameters) {
            assertNotNull(param.getName(), "参数名称不应为空");
            assertFalse(param.getName().trim().isEmpty(), "参数名称不应为空字符串");
            assertNotNull(param.getType(), "参数类型不应为空");
            assertNotNull(param.getDescription(), "参数描述不应为空");
            assertTrue(param.getLegacyIndex() >= 0, "legacyIndex 应该大于等于0");
        }
    }

    /**
     * 测试参数数量统计
     */
    @Test
    @DisplayName("测试参数数量统计")
    void testParameterCounting() {
        Optional<CalculationFunctionDefinition> addDefinitionOpt =
            CalculationFunctionRegistry.getDefinition("add");

        assertTrue(addDefinitionOpt.isPresent(), "add 函数定义应该存在");

        CalculationFunctionDefinition addDefinition = addDefinitionOpt.get();

        // add 函数应该有1个参数
        assertEquals(1, addDefinition.getTotalParameterCount(), "add 函数应该有1个参数");
        assertEquals(1, addDefinition.getRequiredParameterCount(), "add 函数应该有1个必需参数");
        assertEquals(0, addDefinition.getOptionalParameterCount(), "add 函数应该有0个可选参数");
    }

    /**
     * 测试重载函数的参数合并
     */
    @Test
    @DisplayName("测试重载函数的参数合并")
    void testOverloadedFunctionParameterMerging() {
        Optional<CalculationFunctionDefinition> trendDefinitionOpt =
            CalculationFunctionRegistry.getDefinition("getTrendData");

        assertTrue(trendDefinitionOpt.isPresent(), "getTrendData 函数定义应该存在");

        CalculationFunctionDefinition trendDefinition = trendDefinitionOpt.get();

        // getTrendData 函数有多个重载版本，应该正确合并参数
        assertTrue(trendDefinition.getTotalParameterCount() > 1, "getTrendData 应该有多个参数");
        assertTrue(trendDefinition.getRequiredParameterCount() >= 1, "getTrendData 应该至少有1个必需参数");
        assertTrue(trendDefinition.getOptionalParameterCount() >= 0, "getTrendData 应该有可选参数");

        // 验证参数名称
        List<CalculationParameter> parameters = trendDefinition.getParameters();
        boolean hasTargetField = parameters.stream()
            .anyMatch(p -> "targetField".equals(p.getName()));
        assertTrue(hasTargetField, "getTrendData 应该有 targetField 参数");
    }

    /**
     * 测试函数分类功能
     */
    @Test
    @DisplayName("测试函数分类功能")
    void testFunctionCategories() {
        List<String> categories = CalculationFunctionRegistry.getAllCategories();

        assertNotNull(categories, "分类列表不应为空");
        assertFalse(categories.isEmpty(), "应该有至少一个分类");

        // 验证预期的分类是否存在
        assertTrue(categories.contains("算术运算") || categories.contains("arithmetic"),
            "应该包含算术运算分类");
    }

    /**
     * 测试函数统计信息
     */
    @Test
    @DisplayName("测试函数统计信息")
    void testFunctionStatistics() {
        Map<String, Object> stats = CalculationFunctionRegistry.getStatistics();

        assertNotNull(stats, "统计信息不应为空");
        assertTrue(stats.containsKey("totalFunctions"), "应该包含总函数数量");
        assertTrue(stats.containsKey("categoryStats"), "应该包含分类统计");

        Object totalFunctions = stats.get("totalFunctions");
        assertNotNull(totalFunctions, "总函数数量不应为空");
        assertInstanceOf(Integer.class, totalFunctions, "总函数数量应该是整数");
        assertTrue((Integer) totalFunctions > 0, "总函数数量应该大于0");
    }

    /**
     * 测试函数查找功能
     */
    @Test
    @DisplayName("测试函数查找功能")
    void testFunctionLookup() {
        // 测试存在的函数
        Optional<CalculationFunctionDefinition> existingFunction =
            CalculationFunctionRegistry.getDefinition("add");
        assertTrue(existingFunction.isPresent(), "应该能找到存在的函数");

        // 测试不存在的函数
        Optional<CalculationFunctionDefinition> nonExistingFunction =
            CalculationFunctionRegistry.getDefinition("nonExistentFunction");
        assertFalse(nonExistingFunction.isPresent(), "不应该找到不存在的函数");
    }

    /**
     * 测试参数默认值处理
     */
    @Test
    @DisplayName("测试参数默认值处理")
    void testParameterDefaultValues() {
        Optional<CalculationFunctionDefinition> trendDefinitionOpt =
            CalculationFunctionRegistry.getDefinition("getTrendData");

        assertTrue(trendDefinitionOpt.isPresent(), "getTrendData 函数定义应该存在");

        CalculationFunctionDefinition trendDefinition = trendDefinitionOpt.get();
        List<CalculationParameter> parameters = trendDefinition.getParameters();

        // 检查是否有参数设置了默认值
        boolean hasDefaultValues = parameters.stream()
            .anyMatch(p -> !p.isRequired() && p.getDefaultValue() != null);

        if (trendDefinition.getOptionalParameterCount() > 0) {
            assertTrue(hasDefaultValues, "可选参数应该有默认值");
        }
    }

    /**
     * 测试legacyIndex的自动分配
     */
    @Test
    @DisplayName("测试legacyIndex的自动分配")
    void testLegacyIndexAutoAssignment() {
        Optional<CalculationFunctionDefinition> addDefinitionOpt =
            CalculationFunctionRegistry.getDefinition("add");

        assertTrue(addDefinitionOpt.isPresent(), "add 函数定义应该存在");

        CalculationFunctionDefinition addDefinition = addDefinitionOpt.get();
        List<CalculationParameter> parameters = addDefinition.getParameters();

        // 验证legacyIndex是否按顺序分配
        for (int i = 0; i < parameters.size(); i++) {
            CalculationParameter param = parameters.get(i);
            assertEquals(i, param.getLegacyIndex(),
                String.format("参数 %s 的 legacyIndex 应该是 %d", param.getName(), i));
        }
    }
}
