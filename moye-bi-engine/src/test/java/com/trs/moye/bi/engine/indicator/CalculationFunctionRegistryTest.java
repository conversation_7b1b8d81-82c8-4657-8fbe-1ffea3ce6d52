package com.trs.moye.bi.engine.indicator;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.trs.moye.bi.engine.indicator.entity.CalculationFunctionDefinition;
import com.trs.moye.bi.engine.indicator.function.CalculationFunctionRegistry;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * CalculationFunctionRegistry 测试类
 * <p>
 * 专门测试函数注册表的核心功能，包括：
 * <ol>
 *   <li>函数注册表的初始化</li>
 *   <li>getAllFunctions() 方法的正确性</li>
 *   <li>getDefinition() 方法的功能</li>
 *   <li>统计和分类方法的正确性</li>
 * </ol>
 * </p>
 *
 * <AUTHOR>
 * @since 2025/07/29
 */
@DisplayName("函数注册表测试")
class CalculationFunctionRegistryTest {

    /**
     * 测试前的初始化
     * <p>
     * 确保函数注册表在测试执行前已正确初始化。
     * </p>
     */
    @BeforeEach
    void setUp() {
        // 确保函数注册表已初始化
        CalculationFunctionRegistry.ensureInitialized();
    }

    /**
     * 测试注册表初始化状态检查
     * <p>
     * 验证 isInitialized() 和 ensureInitialized() 方法的正确性。
     * </p>
     */
    @Test
    @DisplayName("测试注册表初始化状态检查")
    void testInitializationStatus() {
        // 验证注册表已初始化
        assertTrue(CalculationFunctionRegistry.isInitialized(), "注册表应该已初始化");

        // 调用 ensureInitialized 应该不会有问题
        CalculationFunctionRegistry.ensureInitialized();
        assertTrue(CalculationFunctionRegistry.isInitialized(), "调用 ensureInitialized 后注册表仍应已初始化");
    }

    /**
     * 测试 getAllFunctions() 方法
     * <p>
     * 验证该方法能够返回所有已注册函数的名称列表， 并且列表不为空且按字母顺序排序。
     * </p>
     */
    @Test
    @DisplayName("测试 getAllFunctions() 方法")
    void testGetAllFunctions() {
        List<String> allFunctions = CalculationFunctionRegistry.getAllFunctions();

        // 验证基本属性
        assertNotNull(allFunctions, "函数列表不应为空");
        assertFalse(allFunctions.isEmpty(), "应该有至少一个函数");

        // 验证列表是否按字母顺序排序
        for (int i = 1; i < allFunctions.size(); i++) {
            String current = allFunctions.get(i);
            String previous = allFunctions.get(i - 1);
            assertTrue(current.compareTo(previous) >= 0,
                String.format("函数列表应该按字母顺序排序，但 %s 在 %s 之前", current, previous));
        }

        // 验证包含预期的核心函数
        assertTrue(allFunctions.contains("add"), "应该包含 add 函数");
        assertTrue(allFunctions.contains("subtract"), "应该包含 subtract 函数");
        assertTrue(allFunctions.contains("multiply"), "应该包含 multiply 函数");
        assertTrue(allFunctions.contains("divide"), "应该包含 divide 函数");
    }

    /**
     * 测试 getDefinition() 方法
     * <p>
     * 验证该方法能够正确返回指定函数的定义， 包括存在的函数和不存在的函数的处理。
     * </p>
     */
    @Test
    @DisplayName("测试 getDefinition() 方法")
    void testGetDefinition() {
        // 测试存在的函数
        Optional<CalculationFunctionDefinition> addDefinition =
            CalculationFunctionRegistry.getDefinition("add");
        assertTrue(addDefinition.isPresent(), "add 函数定义应该存在");

        CalculationFunctionDefinition definition = addDefinition.get();
        assertNotNull(definition.getFunctionName(), "函数名称不应为空");
        assertNotNull(definition.getDescription(), "函数描述不应为空");
        assertNotNull(definition.getExecutor(), "函数执行器不应为空");

        // 测试不存在的函数
        Optional<CalculationFunctionDefinition> nonExistentDefinition =
            CalculationFunctionRegistry.getDefinition("nonExistentFunction");
        assertFalse(nonExistentDefinition.isPresent(), "不存在的函数应该返回空的 Optional");
    }

    /**
     * 测试 getStatistics() 方法
     * <p>
     * 验证该方法能够返回正确的统计信息， 包括总函数数量和分类统计。
     * </p>
     */
    @Test
    @DisplayName("测试 getStatistics() 方法")
    void testGetStatistics() {
        Map<String, Object> statistics = CalculationFunctionRegistry.getStatistics();

        assertNotNull(statistics, "统计信息不应为空");
        assertTrue(statistics.containsKey("totalFunctions"), "应该包含总函数数量");
        assertTrue(statistics.containsKey("categoryStats"), "应该包含分类统计");

        Object totalFunctions = statistics.get("totalFunctions");
        assertNotNull(totalFunctions, "总函数数量不应为空");
        assertInstanceOf(Integer.class, totalFunctions, "总函数数量应该是整数");
        assertTrue((Integer) totalFunctions > 0, "总函数数量应该大于0");

        // 验证总函数数量与 getAllFunctions() 返回的数量一致
        List<String> allFunctions = CalculationFunctionRegistry.getAllFunctions();
        assertEquals((int) (Integer) totalFunctions, allFunctions.size(),
            "统计中的总函数数量应该与 getAllFunctions() 返回的数量一致");
    }

    /**
     * 测试 getAllCategories() 方法
     * <p>
     * 验证该方法能够返回所有函数分类的列表。
     * </p>
     */
    @Test
    @DisplayName("测试 getAllCategories() 方法")
    void testGetAllCategories() {
        List<String> categories = CalculationFunctionRegistry.getAllCategories();

        assertNotNull(categories, "分类列表不应为空");
        assertFalse(categories.isEmpty(), "应该有至少一个分类");

        // 验证分类名称不为空
        for (String category : categories) {
            assertNotNull(category, "分类名称不应为空");
            assertFalse(category.trim().isEmpty(), "分类名称不应为空字符串");
        }
    }

    /**
     * 测试 getAllDefinitions() 方法
     * <p>
     * 验证该方法能够返回所有函数定义的映射表。
     * </p>
     */
    @Test
    @DisplayName("测试 getAllDefinitions() 方法")
    void testGetAllDefinitions() {
        Map<String, CalculationFunctionDefinition> allDefinitions =
            CalculationFunctionRegistry.getAllDefinitions();

        assertNotNull(allDefinitions, "函数定义映射表不应为空");
        assertFalse(allDefinitions.isEmpty(), "应该有至少一个函数定义");

        // 验证映射表的大小与 getAllFunctions() 返回的数量一致
        List<String> allFunctions = CalculationFunctionRegistry.getAllFunctions();
        assertEquals(allDefinitions.size(), allFunctions.size(), "函数定义映射表的大小应该与函数列表的大小一致");

        // 验证每个函数都有对应的定义
        for (String functionName : allFunctions) {
            assertTrue(allDefinitions.containsKey(functionName),
                "函数 " + functionName + " 应该在定义映射表中");
            assertNotNull(allDefinitions.get(functionName),
                "函数 " + functionName + " 的定义不应为空");
        }
    }

    /**
     * 测试重新加载功能
     * <p>
     * 验证 reload() 方法能够正确重新加载函数定义。
     * </p>
     */
    @Test
    @DisplayName("测试重新加载功能")
    void testReload() {
        // 记录重新加载前的函数数量
        int beforeReloadCount = CalculationFunctionRegistry.getAllFunctions().size();

        // 执行重新加载
        CalculationFunctionRegistry.reload();

        // 验证重新加载后注册表仍然初始化
        assertTrue(CalculationFunctionRegistry.isInitialized(), "重新加载后注册表应该已初始化");

        // 验证函数数量保持一致（假设没有新增或删除函数）
        int afterReloadCount = CalculationFunctionRegistry.getAllFunctions().size();
        assertEquals(afterReloadCount, beforeReloadCount, "重新加载后函数数量应该保持一致");

        // 验证核心函数仍然存在
        List<String> allFunctions = CalculationFunctionRegistry.getAllFunctions();
        assertTrue(allFunctions.contains("add"), "重新加载后应该仍包含 add 函数");
        assertTrue(allFunctions.contains("subtract"), "重新加载后应该仍包含 subtract 函数");
    }

    /**
     * 测试线程安全性
     * <p>
     * 验证多线程环境下函数注册表的访问是否安全。
     * </p>
     */
    @Test
    @DisplayName("测试线程安全性")
    void testThreadSafety() throws InterruptedException {
        int threadCount = 10;
        Thread[] threads = new Thread[threadCount];
        boolean[] results = new boolean[threadCount];

        // 创建多个线程同时访问注册表
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            threads[i] = new Thread(() -> {
                try {
                    // 多次调用各种方法
                    for (int j = 0; j < 100; j++) {
                        List<String> functions = CalculationFunctionRegistry.getAllFunctions();
                        assertNotNull(functions);
                        assertFalse(functions.isEmpty());

                        Optional<CalculationFunctionDefinition> definition =
                            CalculationFunctionRegistry.getDefinition("add");
                        assertTrue(definition.isPresent());

                        Map<String, Object> stats = CalculationFunctionRegistry.getStatistics();
                        assertNotNull(stats);
                    }
                    results[threadIndex] = true;
                } catch (Exception e) {
                    results[threadIndex] = false;
                }
            });
        }

        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }

        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join(5000); // 最多等待5秒
        }

        // 验证所有线程都成功完成
        for (int i = 0; i < threadCount; i++) {
            assertTrue(results[i], "线程 " + i + " 应该成功完成");
        }
    }
}
