package com.trs.moye.bi.engine.indicator;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.trs.moye.base.data.indicator.entity.IndicatorApplicationFieldConfig;
import com.trs.moye.bi.engine.indicator.context.ParameterContext;
import com.trs.moye.bi.engine.indicator.entity.CalculationFunctionDefinition;
import com.trs.moye.bi.engine.indicator.entity.CalculationParameter;
import com.trs.moye.bi.engine.indicator.function.CalculationFunctionRegistry;
import com.trs.moye.bi.engine.indicator.function.ParameterResolver;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

/**
 * 重载方法智能合并和动态调用测试类
 * <p>
 * 专门测试方法重载的合并和动态调用机制，验证：
 * <ol>
 *   <li>重载方法的正确识别和合并</li>
 *   <li>参数定义的智能分析（必需/可选参数）</li>
 *   <li>运行时智能方法选择</li>
 *   <li>传统参数和命名参数的兼容性</li>
 *   <li>默认值的正确处理</li>
 * </ol>
 * </p>
 *
 * <AUTHOR>
 * @since 2025/07/29
 */
@DisplayName("重载方法测试")
class OverloadMethodTest {

    private CalculationFunctionDefinition getTrendDataDefinition;

    /**
     * 测试前的初始化
     * <p>
     * 确保函数注册表已初始化，并获取测试所需的 getTrendData 函数定义。
     * </p>
     */
    @BeforeEach
    void setUp() {
        // 确保函数注册表已初始化
        CalculationFunctionRegistry.ensureInitialized();

        Optional<CalculationFunctionDefinition> definitionOpt =
            CalculationFunctionRegistry.getDefinition("getTrendData");
        assertTrue(definitionOpt.isPresent(), "getTrendData 函数定义应该存在");
        getTrendDataDefinition = definitionOpt.get();
    }

    /**
     * 测试 getTrendData 重载方法的智能合并
     */
    @Test
    @DisplayName("测试 getTrendData 重载方法的智能合并")
    void testGetTrendDataOverloadMerging() {
        // 验证函数基本信息
        assertEquals("getTrendData", getTrendDataDefinition.getFunctionName());
        assertNotNull(getTrendDataDefinition.getDescription());

        // 验证参数数量
        assertTrue(getTrendDataDefinition.getTotalParameterCount() > 1, "应该有多个参数");
        assertTrue(getTrendDataDefinition.getRequiredParameterCount() >= 1, "应该至少有1个必需参数");
        assertTrue(getTrendDataDefinition.getOptionalParameterCount() > 0, "应该有可选参数");

        // 验证参数详情
        List<CalculationParameter> parameters = getTrendDataDefinition.getParameters();

        // 第一个参数应该是必需的 targetField
        CalculationParameter firstParam = parameters.get(0);
        assertEquals("targetField", firstParam.getName());
        assertTrue(firstParam.isRequired(), "targetField 应该是必需参数");
        assertEquals(0, firstParam.getLegacyIndex(), "第一个参数的 legacyIndex 应该是 0");

        // 验证可选参数有默认值
        boolean hasOptionalParamsWithDefaults = parameters.stream()
            .filter(p -> !p.isRequired())
            .anyMatch(p -> p.getDefaultValue() != null);
        assertTrue(hasOptionalParamsWithDefaults, "可选参数应该有默认值");
    }

    /**
     * 测试传统参数的智能调用
     * <p>
     * 验证系统能够根据传统 List&lt;String&gt; 参数的数量智能选择合适的重载方法， 确保不同参数数量的调用都能正确解析和处理。
     * </p>
     *
     * @param paramString        逗号分隔的参数字符串，代表传统的参数列表
     * @param expectedParamCount 预期的参数数量，用于验证参数解析的正确性
     */
    @ParameterizedTest
    @DisplayName("测试传统参数的智能调用")
    @CsvSource({
        "targetField, 1",
        "'targetField,↑,↓,—', 4",
        "'targetField,↑,↓,—,comma', 5"
    })
    void testLegacyParameterCalls(String paramString, int expectedParamCount) {
        List<String> parameters = Arrays.asList(paramString.split(","));

        IndicatorApplicationFieldConfig config = new IndicatorApplicationFieldConfig();
        config.setFunction("getTrendData");
        config.setInput(parameters);

        ParameterContext context = ParameterResolver.resolve(getTrendDataDefinition, config);

        assertNotNull(context, "参数上下文不应为空");
        assertTrue(context.getParameterCount() > 0, "应该解析出参数");

        // 验证 targetField 参数
        String targetField = context.getString("targetField");
        assertEquals("targetField", targetField, "targetField 参数应该正确解析");
    }

    /**
     * 测试命名参数的智能调用 - 只提供必需参数
     * <p>
     * 验证当只提供必需参数时，系统能够正确解析参数并为可选参数 使用默认值，确保最小参数配置的正确处理。
     * </p>
     */
    @Test
    @DisplayName("测试命名参数调用 - 只提供必需参数")
    void testNamedParameterCallsMinimal() {
        Map<String, String> namedParams = new HashMap<>();
        namedParams.put("targetField", "testField");

        IndicatorApplicationFieldConfig config = new IndicatorApplicationFieldConfig();
        config.setFunction("getTrendData");
        config.setNamedParameters(namedParams);

        ParameterContext context = ParameterResolver.resolve(getTrendDataDefinition, config);

        assertNotNull(context, "参数上下文不应为空");
        assertTrue(context.getParameterCount() > 0, "应该解析出参数");

        // 验证必需参数
        String targetField = context.getString("targetField");
        assertEquals("testField", targetField, "targetField 参数应该正确解析");
    }

    /**
     * 测试命名参数调用 - 提供部分可选参数
     */
    @Test
    @DisplayName("测试命名参数调用 - 提供部分可选参数")
    void testNamedParameterCallsPartial() {
        Map<String, String> namedParams = new HashMap<>();
        namedParams.put("targetField", "testField");
        namedParams.put("upText", "上升");
        namedParams.put("downText", "下降");

        IndicatorApplicationFieldConfig config = new IndicatorApplicationFieldConfig();
        config.setFunction("getTrendData");
        config.setNamedParameters(namedParams);

        ParameterContext context = ParameterResolver.resolve(getTrendDataDefinition, config);

        assertNotNull(context, "参数上下文不应为空");

        // 验证参数值
        assertEquals("testField", context.getString("targetField"));
        assertEquals("上升", context.getString("upText"));
        assertEquals("下降", context.getString("downText"));
    }

    /**
     * 测试命名参数调用 - 提供所有参数
     */
    @Test
    @DisplayName("测试命名参数调用 - 提供所有参数")
    void testNamedParameterCallsFull() {
        Map<String, String> namedParams = new HashMap<>();
        namedParams.put("targetField", "testField");
        namedParams.put("upText", "上升");
        namedParams.put("downText", "下降");
        namedParams.put("equalText", "平稳");
        namedParams.put("separator", ",");

        IndicatorApplicationFieldConfig config = new IndicatorApplicationFieldConfig();
        config.setFunction("getTrendData");
        config.setNamedParameters(namedParams);

        ParameterContext context = ParameterResolver.resolve(getTrendDataDefinition, config);

        assertNotNull(context, "参数上下文不应为空");

        // 验证所有参数值
        assertEquals("testField", context.getString("targetField"));
        assertEquals("上升", context.getString("upText"));
        assertEquals("下降", context.getString("downText"));
        assertEquals("平稳", context.getString("equalText"));
        assertEquals(",", context.getString("separator"));
    }

    /**
     * 测试默认值处理
     */
    @Test
    @DisplayName("测试默认值处理")
    void testDefaultValueHandling() {
        // 创建只包含必需参数的配置
        Map<String, String> namedParams = new HashMap<>();
        namedParams.put("targetField", "testField");

        IndicatorApplicationFieldConfig config = new IndicatorApplicationFieldConfig();
        config.setFunction("getTrendData");
        config.setNamedParameters(namedParams);

        ParameterContext context = ParameterResolver.resolve(getTrendDataDefinition, config);

        assertNotNull(context, "参数上下文不应为空");

        // 验证必需参数
        assertEquals("testField", context.getString("targetField"));

        // 验证默认值是否正确设置（如果有可选参数的话）
        if (getTrendDataDefinition.getOptionalParameterCount() > 0) {
            // 检查是否有默认值被设置
            boolean hasDefaultValues = getTrendDataDefinition.getParameters().stream()
                .filter(p -> !p.isRequired())
                .anyMatch(p -> p.getDefaultValue() != null);

            if (hasDefaultValues) {
                // 这些值可能是默认值，也可能是null，取决于具体实现
                // 主要验证不会抛出异常
                assertNotNull(context, "上下文应该正常创建");
            }
        }
    }

    /**
     * 测试其他重载函数
     * <p>
     * 验证除 getTrendData 之外的其他重载函数是否正确实现了智能合并功能， 确保这些函数的基本属性和参数结构符合预期。
     * </p>
     *
     * @param functionName 要测试的重载函数名称，包括具有多个版本的复杂函数
     */
    @ParameterizedTest
    @DisplayName("测试其他重载函数")
    @CsvSource({
        "dailyAverage",
        "verticalSumEx"
    })
    void testOtherOverloadedFunctions(String functionName) {
        Optional<CalculationFunctionDefinition> definitionOpt =
            CalculationFunctionRegistry.getDefinition(functionName);

        assertTrue(definitionOpt.isPresent(), functionName + " 函数定义应该存在");

        CalculationFunctionDefinition definition = definitionOpt.get();

        // 验证基本属性
        assertEquals(functionName, definition.getFunctionName());
        assertNotNull(definition.getDescription());
        assertTrue(definition.getTotalParameterCount() > 0, "应该有参数");
        assertTrue(definition.getRequiredParameterCount() > 0, "应该有必需参数");
    }

    /**
     * 测试参数数量统计的正确性
     * <p>
     * 验证重载方法合并后的参数数量统计是否准确，包括总参数数量、 必需参数数量和可选参数数量的一致性检查。这是重载方法 智能合并功能的关键验证点。
     * </p>
     */
    @Test
    @DisplayName("测试参数数量统计的正确性")
    void testParameterCountAccuracy() {
        int totalParams = getTrendDataDefinition.getTotalParameterCount();
        int requiredParams = getTrendDataDefinition.getRequiredParameterCount();
        int optionalParams = getTrendDataDefinition.getOptionalParameterCount();

        // 验证数量关系
        assertEquals(totalParams, requiredParams + optionalParams,
            "总参数数量应该等于必需参数数量加可选参数数量");

        // 验证与实际参数列表的一致性
        List<CalculationParameter> parameters = getTrendDataDefinition.getParameters();
        assertEquals(totalParams, parameters.size(), "总参数数量应该与参数列表大小一致");

        long actualRequiredCount = parameters.stream()
            .filter(CalculationParameter::isRequired)
            .count();
        assertEquals(requiredParams, actualRequiredCount, "必需参数数量应该与实际统计一致");

        long actualOptionalCount = parameters.stream()
            .filter(p -> !p.isRequired())
            .count();
        assertEquals(optionalParams, actualOptionalCount, "可选参数数量应该与实际统计一致");
    }
}
