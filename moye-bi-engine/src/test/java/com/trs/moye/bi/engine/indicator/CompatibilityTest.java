package com.trs.moye.bi.engine.indicator;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.trs.moye.base.data.indicator.entity.IndicatorApplicationFieldConfig;
import com.trs.moye.bi.engine.indicator.entity.CalculationFunctionDefinition;
import com.trs.moye.bi.engine.indicator.function.CalculationFunctionRegistry;
import com.trs.moye.bi.engine.indicator.context.ParameterContext;
import com.trs.moye.bi.engine.indicator.function.ParameterResolver;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

/**
 * 兼容性测试类
 * <p>
 * 验证从 IndicatorApplicationFieldConfig.getInput() 到最终方法调用的完整流程， 确保旧版的 config.getInput().get(index) 访问方式在新的注解系统中继续有效。
 * <p>
 * 测试重点：
 * <ol>
 *   <li>完整的参数解析流程</li>
 *   <li>传统参数访问方式的兼容性</li>
 *   <li>新旧系统的一致性验证</li>
 * </ol>
 * </p>
 *
 * <AUTHOR>
 * @since 2025/07/29
 */
@DisplayName("兼容性测试")
class CompatibilityTest {

    /**
     * 测试前的初始化
     * <p>
     * 确保函数注册表在端到端测试执行前已正确初始化。
     * </p>
     */
    @BeforeEach
    void setUp() {
        // 确保函数注册表已初始化
        CalculationFunctionRegistry.ensureInitialized();
    }

    /**
     * 测试传统参数访问方式的完整流程
     * <p>
     * 验证从 IndicatorApplicationFieldConfig.getInput() 到最终参数解析的完整流程， 确保传统的 config.getInput().get(index) 访问方式在新的注解系统中继续有效。
     * 这是向后兼容性的核心测试。
     * </p>
     *
     * @param functionName 要测试的函数名称，包括各种类型的计算函数
     * @param paramString  逗号分隔的参数字符串，代表传统的参数配置方式
     */
    @ParameterizedTest
    @DisplayName("测试传统参数访问方式的完整流程")
    @CsvSource({
        "add, 'field1,field2,field3'",
        "getTrendData, 'field1'",
        "getTrendData, 'field1,↑,↓,—'",
        "verticalSum, 'sales,region,product,total,合计'",
        "dailyAverage, 'sales,2'"
    })
    void testLegacyParameterAccessFlow(String functionName, String paramString) {
        List<String> parameters = Arrays.asList(paramString.split(","));

        Optional<CalculationFunctionDefinition> definitionOpt =
            CalculationFunctionRegistry.getDefinition(functionName);
        assertTrue(definitionOpt.isPresent(), "函数定义应该存在: " + functionName);

        IndicatorApplicationFieldConfig config = new IndicatorApplicationFieldConfig();
        config.setFunction(functionName);
        config.setInput(parameters);

        // 测试传统的 getInput().get(index) 访问方式
        List<String> inputList = config.getInput();
        assertNotNull(inputList, "输入列表不应为空");
        assertEquals(parameters.size(), inputList.size(), "输入列表大小应该匹配");

        for (int i = 0; i < inputList.size(); i++) {
            String value = inputList.get(i);
            assertEquals(parameters.get(i), value,
                String.format("传统访问 config.getInput().get(%d) 应该返回正确值", i));
        }

        // 测试新的参数解析
        CalculationFunctionDefinition definition = definitionOpt.get();
        ParameterContext context = ParameterResolver.resolve(definition, config);
        assertNotNull(context, "参数上下文不应为空");
        assertTrue(context.getParameterCount() > 0, "应该解析出参数");
    }

    /**
     * 测试新旧系统的一致性 - getTrendData
     */
    @Test
    @DisplayName("测试新旧系统的一致性 - getTrendData 基础版本")
    void testNewOldSystemConsistencyGetTrendDataBasic() {
        String functionName = "getTrendData";
        List<String> legacyParameters = List.of("field1");
        Map<String, String> namedParameters = Map.of("targetField", "field1");

        testConsistency(functionName, legacyParameters, namedParameters);
    }

    /**
     * 测试新旧系统的一致性 - getTrendData 完整版本
     */
    @Test
    @DisplayName("测试新旧系统的一致性 - getTrendData 完整版本")
    void testNewOldSystemConsistencyGetTrendDataFull() {
        String functionName = "getTrendData";
        List<String> legacyParameters = Arrays.asList("field1", "上升", "下降", "平稳", ",");
        Map<String, String> namedParameters = Map.of(
            "targetField", "field1",
            "upText", "上升",
            "downText", "下降",
            "equalText", "平稳",
            "separator", ","
        );

        testConsistency(functionName, legacyParameters, namedParameters);
    }

    /**
     * 测试新旧系统的一致性 - dailyAverage 精度版本
     */
    @Test
    @DisplayName("测试新旧系统的一致性 - dailyAverage 精度版本")
    void testNewOldSystemConsistencyDailyAveragePrecision() {
        String functionName = "dailyAverage";
        List<String> legacyParameters = Arrays.asList("sales", "2");
        Map<String, String> namedParameters = Map.of(
            "targetField", "sales",
            "precision", "2"
        );

        testConsistency(functionName, legacyParameters, namedParameters);
    }


    /**
     * 测试新旧系统一致性的通用方法
     *
     * @param functionName     函数名称
     * @param legacyParameters 旧参数
     * @param namedParameters  命名参数
     */
    private void testConsistency(String functionName, List<String> legacyParameters,
        Map<String, String> namedParameters) {
        Optional<CalculationFunctionDefinition> definitionOpt =
            CalculationFunctionRegistry.getDefinition(functionName);
        assertTrue(definitionOpt.isPresent(), "函数定义应该存在: " + functionName);

        CalculationFunctionDefinition definition = definitionOpt.get();

        // 测试传统参数方式
        IndicatorApplicationFieldConfig legacyConfig = new IndicatorApplicationFieldConfig();
        legacyConfig.setFunction(functionName);
        legacyConfig.setInput(legacyParameters);
        ParameterContext legacyContext = ParameterResolver.resolve(definition, legacyConfig);

        // 测试命名参数方式
        IndicatorApplicationFieldConfig namedConfig = new IndicatorApplicationFieldConfig();
        namedConfig.setFunction(functionName);
        namedConfig.setNamedParameters(namedParameters);
        ParameterContext namedContext = ParameterResolver.resolve(definition, namedConfig);

        // 比较结果
        assertNotNull(legacyContext, "传统参数上下文不应为空");
        assertNotNull(namedContext, "命名参数上下文不应为空");

        // 比较关键参数值
        for (String paramName : legacyContext.getParameterNames()) {
            Object legacyValue = legacyContext.getObject(paramName);
            Object namedValue = namedContext.getObject(paramName);

            assertEquals(legacyValue, namedValue, String.format("参数 %s 的值应该一致: legacy=%s, named=%s",
                paramName, legacyValue, namedValue));
        }
    }
}
