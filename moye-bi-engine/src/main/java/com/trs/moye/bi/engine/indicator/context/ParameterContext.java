package com.trs.moye.bi.engine.indicator.context;

import com.trs.moye.base.data.service.enums.EvaluateOperator;
import com.trs.moye.bi.engine.indicator.util.ExceptionLogOptimizer;
import com.trs.moye.bi.engine.indicator.util.ExceptionType;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 参数上下文
 * <p>
 * 包含解析后的参数值，提供类型安全的参数获取方法 支持从命名参数和传统列表参数中获取值
 * </p>
 *
 * <AUTHOR>
 * @since 2025/07/29
 */
@Slf4j
@Data
public class ParameterContext {

    /**
     * 解析后的参数值映射
     * <p>
     * 键为参数名称，值为解析后的参数值（已进行类型转换）
     * </p>
     */
    private final Map<String, Object> parameters = new HashMap<>();

    /**
     * 原始配置信息（用于调试和智能方法选择）
     */
    private final Map<String, Object> rawConfig = new HashMap<>();

    /**
     * 添加参数值
     *
     * @param name  参数名称
     * @param value 参数值
     */
    public void addParameter(String name, Object value) {
        parameters.put(name, value);
    }

    /**
     * 添加原始配置信息
     *
     * @param key   配置键
     * @param value 配置值
     */
    public void addRawConfig(String key, Object value) {
        rawConfig.put(key, value);
    }

    /**
     * 获取字符串参数
     *
     * @param name 参数名称
     * @return 参数值，如果不存在返回null
     */
    public String getString(String name) {
        Object value = parameters.get(name);
        return value != null ? value.toString() : null;
    }

    /**
     * 获取字符串参数（带默认值）
     *
     * @param name         参数名称
     * @param defaultValue 默认值
     * @return 参数值，如果不存在返回默认值
     */
    public String getString(String name, String defaultValue) {
        String value = getString(name);
        return value != null ? value : defaultValue;
    }

    /**
     * 获取整数参数
     *
     * @param name 参数名称
     * @return 参数值，如果不存在或转换失败返回null
     */
    public Integer getInteger(String name) {
        Object value = parameters.get(name);
        if (value == null) {
            return null;
        }

        try {
            if (value instanceof Integer integer) {
                return integer;
            }
            return Integer.valueOf(value.toString());
        } catch (NumberFormatException e) {
            ExceptionLogOptimizer.logSampledWarning(ExceptionType.PARSE_ERROR, "参数 {} 无法转换为整数: {}", name,
                value);
            return null;
        }
    }

    /**
     * 获取整数参数（带默认值）
     *
     * @param name         参数名称
     * @param defaultValue 默认值
     * @return 参数值，如果不存在或转换失败返回默认值
     */
    public Integer getInteger(String name, Integer defaultValue) {
        Integer value = getInteger(name);
        return value != null ? value : defaultValue;
    }

    /**
     * 获取布尔参数
     *
     * @param name 参数名称
     * @return 参数值，如果不存在返回null
     */
    public Boolean getBoolean(String name) {
        Object value = parameters.get(name);
        if (value == null) {
            return null;
        }

        if (value instanceof Boolean v) {
            return v;
        }

        String strValue = value.toString().toLowerCase();
        return "true".equals(strValue) || "1".equals(strValue) || "yes".equals(strValue);
    }

    /**
     * 获取布尔参数（带默认值）
     *
     * @param name         参数名称
     * @param defaultValue 默认值
     * @return 参数值，如果不存在返回默认值
     */
    public Boolean getBoolean(String name, Boolean defaultValue) {
        Boolean value = getBoolean(name);
        return value != null ? value : defaultValue;
    }

    /**
     * 获取枚举参数
     *
     * @param name      参数名称
     * @param enumClass 枚举类型
     * @param <T>       枚举类型
     * @return 参数值，如果不存在或转换失败返回null
     */
    @SuppressWarnings("unchecked")
    public <T extends Enum<T>> T getEnum(String name, Class<T> enumClass) {
        Object value = parameters.get(name);
        if (value == null) {
            return null;
        }

        try {
            if (enumClass.isInstance(value)) {
                return (T) value;
            }
            return Enum.valueOf(enumClass, value.toString());
        } catch (IllegalArgumentException e) {
            ExceptionLogOptimizer.logSampledWarning(ExceptionType.PARSE_ERROR, "参数 {} 无法转换为枚举 {}: {}", name,
                enumClass.getSimpleName(), value);
            return null;
        }
    }

    /**
     * 获取 EvaluateOperator 枚举参数
     *
     * @param name 参数名称
     * @return 参数值，如果不存在或转换失败返回null
     */
    public EvaluateOperator getEvaluateOperator(String name) {
        return getEnum(name, EvaluateOperator.class);
    }

    /**
     * 获取字符串列表参数
     *
     * @param name 参数名称
     * @return 参数值，如果不存在返回null
     */
    public List<String> getStringList(String name) {
        Object value = parameters.get(name);
        if (value instanceof List) {
            return (List<String>) value;
        }
        return Collections.emptyList();
    }

    /**
     * 获取原始对象参数
     *
     * @param name 参数名称
     * @return 参数值，如果不存在返回null
     */
    public Object getObject(String name) {
        return parameters.get(name);
    }

    /**
     * 检查参数是否存在
     *
     * @param name 参数名称
     * @return 如果参数存在返回true，否则返回false
     */
    public boolean hasParameter(String name) {
        return parameters.containsKey(name);
    }

    /**
     * 获取所有参数名称
     *
     * @return 参数名称集合
     */
    public java.util.Set<String> getParameterNames() {
        return parameters.keySet();
    }

    /**
     * 获取参数数量
     *
     * @return 参数数量
     */
    public int getParameterCount() {
        return parameters.size();
    }

    /**
     * 获取原始配置信息
     * <p>
     * 用于智能方法选择和调试，包含原始的配置数据
     * </p>
     *
     * @return 原始配置信息映射
     */
    public Map<String, Object> getRawConfig() {
        return new HashMap<>(rawConfig);
    }
}
