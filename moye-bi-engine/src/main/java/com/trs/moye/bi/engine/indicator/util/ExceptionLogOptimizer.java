package com.trs.moye.bi.engine.indicator.util;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import lombok.extern.slf4j.Slf4j;

/**
 * 异常日志优化
 * <p>
 * 通过采样和统计的方式优化异常日志记录，避免在大量异常数据情况下频繁输出日志影响性能
 * </p>
 *
 * <AUTHOR>
 * @since 2025/07/28
 */
@Slf4j
public final class ExceptionLogOptimizer {

    /**
     * 默认采样率：每100个异常记录1个详细日志
     */
    private static final int DEFAULT_SAMPLE_RATE = 100;

    /**
     * 异常统计信息存储
     */
    private static final ConcurrentHashMap<String, ExceptionStats> EXCEPTION_STATS = new ConcurrentHashMap<>();

    /**
     * 全局异常计数器
     */
    private static final AtomicLong GLOBAL_EXCEPTION_COUNT = new AtomicLong(0);

    /**
     * 是否启用详细异常日志记录
     */
    private static volatile boolean enableDetailedLogging = true;

    /**
     * 采样率配置
     */
    private static volatile int sampleRate = DEFAULT_SAMPLE_RATE;

    private ExceptionLogOptimizer() {
    }

    /**
     * 记录异常日志（采样模式）
     * <p>
     * 根据采样率决定是否记录详细日志，同时更新异常统计信息。 采样策略：每 sampleRate 个异常记录1个详细日志。
     * </p>
     *
     * @param exceptionType 异常类型枚举
     * @param message       日志消息模板
     * @param args          日志参数
     */
    public static void logSampledWarning(ExceptionType exceptionType, String message, Object... args) {
        // 更新统计信息
        String typeKey = exceptionType.getCode();
        ExceptionStats stats = EXCEPTION_STATS.computeIfAbsent(typeKey, k -> new ExceptionStats());
        long currentCount = stats.incrementCount();
        long globalCount = GLOBAL_EXCEPTION_COUNT.incrementAndGet();

        // 采样记录详细日志
        if (enableDetailedLogging && shouldLogDetail(currentCount)) {
            // 构建完整的日志消息，包含原始消息和统计信息
            String fullMessage = message + " [异常统计: 类型={}, 描述={}, 当前计数={}, 全局计数={}]";
            Object[] fullArgs = combineArgs(args, exceptionType.getCode(), exceptionType.getDescription(),
                currentCount, globalCount);
            log.warn(fullMessage, fullArgs);
        }
    }

    /**
     * 记录异常日志（采样模式）- ERROR级别
     * <p>
     * 根据采样率决定是否记录详细日志，同时更新异常统计信息。
     * </p>
     *
     * @param exceptionType 异常类型枚举
     * @param message       日志消息模板
     * @param throwable     异常对象
     * @param args          日志参数
     */
    public static void logSampledError(ExceptionType exceptionType, String message,
        Throwable throwable, Object... args) {
        // 更新统计信息
        String typeKey = exceptionType.getCode();
        ExceptionStats stats = EXCEPTION_STATS.computeIfAbsent(typeKey, k -> new ExceptionStats());
        long currentCount = stats.incrementCount();
        long globalCount = GLOBAL_EXCEPTION_COUNT.incrementAndGet();

        // 采样记录详细日志
        if (enableDetailedLogging && shouldLogDetail(currentCount)) {
            // 构建完整的日志消息，包含原始消息和统计信息
            String fullMessage = message + " [异常统计: 类型={}, 描述={}, 当前计数={}, 全局计数={}]";
            Object[] fullArgs = combineArgs(args, exceptionType.getCode(), exceptionType.getDescription(),
                currentCount, globalCount);
            log.error(fullMessage, fullArgs, throwable);
        }
    }

    /**
     * 输出异常统计摘要.
     * <p>
     * 输出当前所有异常类型的统计信息，用于监控和问题排查。 建议在数据处理完成后调用此方法。
     * </p>
     */
    public static void logExceptionSummary() {
        if (EXCEPTION_STATS.isEmpty()) {
            return;
        }

        log.info("=== 异常统计摘要 ===");
        log.info("全局异常总数: {}", GLOBAL_EXCEPTION_COUNT.get());
        log.info("异常类型统计:");

        EXCEPTION_STATS.forEach((typeCode, stats) -> {
            ExceptionType exceptionType = ExceptionType.fromCode(typeCode);
            log.info("  - {} ({}): {} 次异常", typeCode, exceptionType.getDescription(), stats.getCount());
        });

        log.info("采样率: 每 {} 个异常记录 1 个详细日志", sampleRate);
        log.info("详细日志记录: {}", enableDetailedLogging ? "启用" : "禁用");
        log.info("=== 异常统计摘要结束 ===");
    }

    /**
     * 清理异常统计信息
     * <p>
     * 清空所有统计数据，通常在处理新的数据批次前调用
     * </p>
     */
    public static void clearStats() {
        EXCEPTION_STATS.clear();
        GLOBAL_EXCEPTION_COUNT.set(0);
        log.debug("异常统计信息已清理");
    }

    /**
     * 设置采样率
     * <p>
     * 动态调整采样率，值越大采样越少，性能越好但信息越少
     * </p>
     *
     * @param rate 采样率，必须大于0
     */
    public static void setSampleRate(int rate) {
        if (rate <= 0) {
            throw new IllegalArgumentException("采样率必须大于0");
        }
        sampleRate = rate;
        log.info("异常日志采样率已设置为: {}", rate);
    }

    /**
     * 启用或禁用详细异常日志记录
     * <p>
     * 在高负载场景下可以完全禁用详细日志记录以提升性能
     * </p>
     *
     * @param enabled 是否启用详细日志记录
     */
    public static void setDetailedLoggingEnabled(boolean enabled) {
        enableDetailedLogging = enabled;
        log.info("详细异常日志记录已{}", enabled ? "启用" : "禁用");
    }

    /**
     * 获取指定异常类型的统计信息.
     *
     * @param exceptionType 异常类型枚举
     * @return 异常统计信息，如果不存在则返回null
     */
    public static ExceptionStats getStats(ExceptionType exceptionType) {
        return EXCEPTION_STATS.get(exceptionType.getCode());
    }

    /**
     * 获取全局异常总数
     *
     * @return 全局异常总数
     */
    public static long getGlobalExceptionCount() {
        return GLOBAL_EXCEPTION_COUNT.get();
    }

    /**
     * 判断是否应该记录详细日志
     *
     * @param count 当前异常计数
     * @return 是否应该记录详细日志
     */
    private static boolean shouldLogDetail(long count) {
        return count == 1 || count % sampleRate == 0;
    }

    /**
     * 合并日志参数
     *
     * @param originalArgs   原始参数
     * @param additionalArgs 附加参数
     * @return 合并后的参数数组
     */
    private static Object[] combineArgs(Object[] originalArgs, Object... additionalArgs) {
        Object[] combined = new Object[originalArgs.length + additionalArgs.length];
        System.arraycopy(originalArgs, 0, combined, 0, originalArgs.length);
        System.arraycopy(additionalArgs, 0, combined, originalArgs.length, additionalArgs.length);
        return combined;
    }

    /**
     * 异常统计信息
     */
    public static class ExceptionStats {

        private final AtomicLong count = new AtomicLong(0);

        /**
         * 增加异常计数
         *
         * @return 增加后的计数值
         */
        public long incrementCount() {
            return count.incrementAndGet();
        }

        /**
         * 获取异常计数
         *
         * @return 当前异常计数
         */
        public long getCount() {
            return count.get();
        }
    }
}
