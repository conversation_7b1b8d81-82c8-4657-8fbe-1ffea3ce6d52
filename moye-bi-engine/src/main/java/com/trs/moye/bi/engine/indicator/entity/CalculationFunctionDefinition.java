package com.trs.moye.bi.engine.indicator.entity;

import com.trs.moye.bi.engine.indicator.context.ParameterContext;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 计算函数定义
 * <p>
 * 定义计算函数的完整信息，包括函数名称、参数结构和执行逻辑 用于支持参数解析、验证和函数执行
 * </p>
 *
 * <AUTHOR>
 * @since 2025/07/29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CalculationFunctionDefinition {

    /**
     * 函数名称
     * <p>
     * 与 CalculationFunction 枚举中的 functionName 对应
     * </p>
     */
    private String functionName;

    /**
     * 函数描述
     * <p>
     * 用于界面显示和文档生成，描述函数的用途和功能
     * </p>
     */
    private String description;

    /**
     * 参数定义列表
     * <p>
     * 定义该函数所需的所有参数，包括必需参数和可选参数
     * </p>
     */
    private List<CalculationParameter> parameters;

    /**
     * 函数执行器
     * <p>
     * 接收解析后的参数上下文，执行具体的计算逻辑
     * </p>
     */
    private Function<ParameterContext, Map<String, Object>> executor;

    /**
     * 函数分类
     * <p>
     * 用于界面分组显示，如：算术运算、比较分析、数据聚合等
     * </p>
     */
    private String category;

    /**
     * 是否为内部函数
     * <p>
     * 内部函数不在界面中显示，仅供系统内部使用
     * </p>
     */
    private boolean internal;

    /**
     * 主要方法对象
     * <p>
     * 用于获取函数的注解信息和反射调用
     * 通常是参数最多的方法（包含所有可能的参数）
     * </p>
     */
    private Method primaryMethod;


    /**
     * 创建带分类的函数定义
     *
     * @param functionName 函数名称
     * @param description  函数描述
     * @param category     函数分类
     * @param parameters   参数定义列表
     * @param executor     函数执行器
     * @return 函数定义对象
     */
    public static CalculationFunctionDefinition create(String functionName,
        String description,
        String category,
        List<CalculationParameter> parameters,
        Function<ParameterContext, Map<String, Object>> executor) {
        return CalculationFunctionDefinition.builder()
            .functionName(functionName)
            .description(description)
            .category(category)
            .parameters(parameters)
            .executor(executor)
            .internal(false)
            .build();
    }


    /**
     * 执行函数
     *
     * @param context 参数上下文
     * @return 计算结果
     */
    public Map<String, Object> execute(ParameterContext context) {
        if (executor == null) {
            throw new IllegalStateException("函数执行器未定义: " + functionName);
        }
        return executor.apply(context);
    }

    /**
     * 获取必需参数数量
     *
     * @return 必需参数数量
     */
    public int getRequiredParameterCount() {
        return (int) parameters.stream().filter(CalculationParameter::isRequired).count();
    }

    /**
     * 获取可选参数数量
     *
     * @return 可选参数数量
     */
    public int getOptionalParameterCount() {
        return parameters.size() - getRequiredParameterCount();
    }

    /**
     * 获取参数总数
     *
     * @return 参数总数
     */
    public int getTotalParameterCount() {
        return parameters.size();
    }

    /**
     * 根据名称查找参数定义
     *
     * @param parameterName 参数名称
     * @return 参数定义，如果不存在返回null
     */
    public CalculationParameter findParameter(String parameterName) {
        return parameters.stream()
            .filter(p -> p.getName().equals(parameterName))
            .findFirst()
            .orElse(null);
    }

    /**
     * 检查是否有指定名称的参数
     *
     * @param parameterName 参数名称
     * @return 如果存在返回true，否则返回false
     */
    public boolean hasParameter(String parameterName) {
        return findParameter(parameterName) != null;
    }
}
