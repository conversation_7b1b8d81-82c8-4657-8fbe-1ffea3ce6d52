package com.trs.moye.bi.engine.indicator.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 指标计算函数注解
 * <p>
 * 用于标注指标计算函数方法本身，包括函数名称、描述、分类等信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025/07/29
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface IndicatorFunction {

    /**
     * 函数英文名称
     * <p>
     * 与 CalculationFunction 枚举中的 functionName 对应 必须唯一，用于函数的识别和调用
     * </p>
     *
     * @return 函数英文名称
     */
    String enName();

    /**
     * 函数中文名称
     * <p>
     * 用于界面显示的中文名称，便于用户理解
     * </p>
     *
     * @return 函数中文名称
     */
    String zhName();

    /**
     * 函数描述
     * <p>
     * 用于界面显示和文档生成，描述函数的用途和功能 应该详细说明函数的作用、适用场景和注意事项
     * </p>
     *
     * @return 函数描述
     */
    String description();

    /**
     * 数据生成类型
     * <p>
     * 标识该函数是否会生成新的数据或修改现有数据结构
     * </p>
     * <ul>
     *   <li>{@link DataGenerationType#NORMAL} - 普通指标能力，不生成新数据（默认值）</li>
     *   <li>{@link DataGenerationType#DATA_GENERATOR} - 生成新数据的能力，会创建新的字段和值</li>
     * </ul>
     *
     * @return 数据生成类型
     */
    DataGenerationType dataGenerationType() default DataGenerationType.NORMAL;

}
