package com.trs.moye.bi.engine.indicator.v1.indicator;

import com.trs.moye.base.data.indicator.entity.IndicatorApplicationFieldConfig;
import com.trs.moye.base.data.indicator.enums.IndicatorStatOperation;
import com.trs.moye.base.data.indicator.enums.IndicatorStatType;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import org.springframework.stereotype.Component;

/**
 * 总数趋势变化操作
 */
@Component
public class TotalTrendChangeOperation extends AbstractStatOperation {

    @Override
    public void calculate(List<Map<String, Object>> currentData, List<Map<String, Object>> historyData,
        IndicatorStatParams params) {
        String targetField = getTargetField(params);
        String resultField = getResultField(params);
        String startTime = params.getStartTimeField();
        List<String> dims = getStatisticDims(params);
        IndicatorApplicationFieldConfig config = params.getFieldConfig();
        IndicatorStatType indicatorStatType = config.getIndicatorStatType();

        for (Map<String, Object> data : currentData) {
            List<String> trends = computeTrendList(historyData, data, dims, startTime, targetField);
            String trendResult;
            // 根据trendType判断是计算哪种趋势
            if (IndicatorStatType.CONTINUOUS == indicatorStatType) {
                trendResult = calculateContinuousTrend(trends);
            } else {
                trendResult = String.join(" ", trends);
            }
            data.put(resultField, trendResult);
        }
    }

    private List<String> computeTrendList(List<Map<String, Object>> history, Map<String, Object> current,
        List<String> dims, String timeField, String targetField) {
        List<Entry<LocalDateTime, Map<String, Object>>> timed = buildTimedEntries(history, current, dims, timeField);
        if (timed.size() < 2) {
            return new ArrayList<>();
        }
        List<String> trends = new ArrayList<>();
        for (int i = 1; i < timed.size(); i++) {
            Number prevNum = safeGetNumber(timed.get(i - 1).getValue(), targetField);
            Number curNum = safeGetNumber(timed.get(i).getValue(), targetField);
            double prev = prevNum != null ? prevNum.doubleValue() : 0d;
            double cur = curNum != null ? curNum.doubleValue() : 0d;
            String trend;
            if (cur > prev) {
                trend = "↑";
            } else if (cur < prev) {
                trend = "↓";
            } else {
                trend = "—";
            }
            trends.add(trend);
        }
        return trends;
    }

    private String calculateContinuousTrend(List<String> trends) {
        if (trends.isEmpty()) {
            return "";
        }

        String lastTrend = trends.get(trends.size() - 1);
        // 只计算上升趋势
        if (!"↑".equals(lastTrend)) {
            return "无连续上升趋势";
        }

        int continuousCount = 0;
        for (int i = trends.size() - 1; i >= 0; i--) {
            if (trends.get(i).equals(lastTrend)) {
                continuousCount++;
            } else {
                break;
            }
        }

        if (continuousCount > 1) {
            return "连续两日上升";
        }

        return "";
    }


    @Override
    public boolean isSupport(IndicatorStatOperation operation) {
        return IndicatorStatOperation.TREND_CHANGE.equals(operation);
    }

}
