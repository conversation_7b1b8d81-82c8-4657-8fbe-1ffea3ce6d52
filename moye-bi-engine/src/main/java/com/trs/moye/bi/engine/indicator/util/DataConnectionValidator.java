package com.trs.moye.bi.engine.indicator.util;

import com.trs.moye.base.data.connection.dao.DataConnectionMapper;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.indicator.dao.IndicatorConfigMapper;
import com.trs.moye.base.data.indicator.dao.IndicatorPeriodConfigMapper;
import com.trs.moye.base.data.indicator.entity.IndicatorConfig;
import com.trs.moye.base.data.indicator.entity.IndicatorDataSearchParams;
import com.trs.moye.base.data.indicator.entity.IndicatorPeriodConfigEntity;
import com.trs.moye.bi.engine.indicator.IndicatorServiceNew.FieldGroups;
import com.trs.moye.bi.engine.indicator.context.QueryContext;
import com.trs.moye.bi.engine.indicator.exception.IndicatorQueryException;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 数据连接验证器
 * <p>
 * 负责验证数据连接和构建查询上下文
 *
 * <AUTHOR>
 * @since 2025/07/23
 */
@Slf4j
@Component
public class DataConnectionValidator {

    @Resource
    private DataConnectionMapper dataConnectionMapper;

    @Resource
    private IndicatorConfigMapper indicatorConfigMapper;

    @Resource
    private IndicatorPeriodConfigMapper indicatorPeriodConfigMapper;

    /**
     * 验证数据连接并返回连接对象.
     *
     * @param connectionId 连接ID
     * @return 数据连接对象
     * @throws IndicatorQueryException 当连接不存在时
     */
    public DataConnection validateAndGetConnection(final Integer connectionId) {
        log.debug("验证数据连接, connectionId: {}", connectionId);

        if (connectionId == null) {
            throw IndicatorQueryException.validationError("数据连接ID不能为空");
        }

        DataConnection dataConnection = dataConnectionMapper.selectById(connectionId);
        if (dataConnection == null) {
            log.warn("数据连接不存在, connectionId: {}", connectionId);
            throw IndicatorQueryException.connectionNotFound(connectionId);
        }

        log.debug("数据连接验证成功, connectionId: {}, connectionName: {}",
            connectionId, dataConnection.getName());
        return dataConnection;
    }

    /**
     * 构建完整的查询上下文.
     *
     * @param dataConnection 数据连接
     * @param request        查询参数
     * @param fieldGroups    字段组
     * @return 查询上下文
     */
    public QueryContext buildQueryContext(final DataConnection dataConnection,
        final IndicatorDataSearchParams request,
        final FieldGroups fieldGroups) {
        log.debug("构建查询上下文, dataModelId: {}", request.getDataModelId());

        try {
            // 获取指标配置
            IndicatorConfig indicatorConfig = indicatorConfigMapper.selectByDataModelId(
                request.getDataModelId());

            // 获取周期配置
            IndicatorPeriodConfigEntity periodConfig = null;
            if (indicatorConfig != null && indicatorConfig.getStatisticStrategyInfo() != null) {
                periodConfig = indicatorPeriodConfigMapper.selectByPeriodType(
                    indicatorConfig.getStatisticStrategyInfo().getPeriod().name());
            }

            QueryContext context = QueryContext.builder()
                .dataConnection(dataConnection)
                .indicatorConfig(indicatorConfig)
                .fieldGroups(fieldGroups)
                .request(request)
                .periodConfig(periodConfig)
                .build();

            log.debug("查询上下文构建成功, dataModelId: {}, hasIndicatorConfig: {}, "
                    + "hasPeriodConfig: {}",
                request.getDataModelId(), indicatorConfig != null, periodConfig != null);

            return context;

        } catch (Exception e) {
            log.error("构建查询上下文失败, dataModelId: {}", request.getDataModelId(), e);
            throw IndicatorQueryException.dataProcessingError("构建查询上下文", e);
        }
    }

    /**
     * 验证查询参数.
     *
     * @param request 查询参数
     * @throws IndicatorQueryException 当参数无效时
     */
    public void validateRequest(final IndicatorDataSearchParams request) {
        if (request == null) {
            throw IndicatorQueryException.validationError("查询参数不能为空");
        }

        if (request.getDataModelId() == null) {
            throw IndicatorQueryException.validationError("数据模型ID不能为空");
        }

        if (request.getTableName() == null || request.getTableName().trim().isEmpty()) {
            throw IndicatorQueryException.validationError("表名不能为空");
        }

        log.debug("查询参数验证通过, dataModelId: {}, tableName: {}",
            request.getDataModelId(), request.getTableName());
    }
}
