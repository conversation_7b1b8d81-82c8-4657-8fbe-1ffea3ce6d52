package com.trs.moye.bi.engine.indicator.util;

import lombok.Getter;

/**
 * 异常类型枚举
 * <p>
 * 定义系统中各种异常类型的标准化标识，用于异常日志优化器的统计和分类 每个枚举值包含唯一标识和描述信息，便于异常统计和问题排查
 * </p>
 *
 * <AUTHOR>
 * @since 2025/07/28
 */
@Getter
public enum ExceptionType {

    /**
     * 解析异常
     * <p>
     * 当无法将输入值解析为有效的格式时触发， 常见于字段处理等场景
     * </p>
     */
    PARSE_ERROR("TIME_PARSE_ERROR", "时间解析异常"),

    /**
     * 对象比较异常
     * <p>
     * 当无法比较两个对象的大小关系时触发， 常见于排序、条件判断等场景。
     * </p>
     */
    COMPARE_ERROR("COMPARE_ERROR", "对象比较异常"),
    ;

    /**
     * 异常类型的唯一标识
     */
    private final String code;

    /**
     * 异常类型的中文描述
     */
    private final String description;

    /**
     * 构造函数
     *
     * @param code        异常类型的唯一标识
     * @param description 异常类型的中文描述
     */
    ExceptionType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码查找对应的异常类型
     * <p>
     * 提供从字符串代码到枚举值的转换，支持向后兼容和动态查找。
     * </p>
     *
     * @param code 异常类型代码
     * @return 对应的异常类型枚举值，如果未找到则返回
     */
    public static ExceptionType fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }

        for (ExceptionType type : values()) {
            if (type.code.equals(code.trim())) {
                return type;
            }
        }

        return null;
    }
}
