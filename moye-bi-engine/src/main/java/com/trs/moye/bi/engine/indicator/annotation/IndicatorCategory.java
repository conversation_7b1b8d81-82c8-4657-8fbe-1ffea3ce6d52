package com.trs.moye.bi.engine.indicator.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 指标计算函数分类注解
 * <p>
 * 用于标注指标计算函数类的分类信息，便于函数的组织和管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025/07/29
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE})
public @interface IndicatorCategory {

    /**
     * 分类英文缩写
     * <p>
     * 用于系统内部识别，应该简洁明了 例如：arithmetic, comparative, aggregation
     * </p>
     *
     * @return 分类英文缩写
     */
    String enAbbr();

    /**
     * 分类名称
     * <p>
     * 用于界面显示的分类名称 例如：算术运算、比较分析、数据聚合
     * </p>
     *
     * @return 分类名称
     */
    String name();

    /**
     * 分类描述
     * <p>
     * 详细描述该分类下函数的特点和用途
     * </p>
     *
     * @return 分类描述，默认为空字符串
     */
    String description() default "";

    /**
     * 分类排序
     * <p>
     * 用于界面显示时的排序，数值越小越靠前
     * </p>
     *
     * @return 排序值，默认为0
     */
    int order() default 0;

    /**
     * 分类图标
     * <p>
     * 用于界面显示的图标名称或路径
     * </p>
     *
     * @return 图标名称，默认为空字符串
     */
    String icon() default "";
}
