package com.trs.moye.bi.engine.indicator.processor.condition;

import com.trs.moye.base.data.service.entity.Condition;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

/**
 * 条件处理器
 * <p>
 * 统一处理SQL条件和内存条件的集成类，提供以下功能：
 * <ul>
 *   <li>内存过滤：对计算结果应用内存条件过滤</li>
 * </ul>
 * </p>
 *
 * <AUTHOR>
 * @since 2025/08/13
 */
@Slf4j
public class ConditionProcessor {

    /**
     * 应用内存条件过滤
     * <p>
     * 对数据列表应用内存条件进行过滤
     * </p>
     *
     * @param data             待过滤的数据列表
     * @param memoryConditions 内存条件列表
     * @return 过滤后的数据列表
     */
    public static List<Map<String, Object>> applyMemoryConditions(List<Map<String, Object>> data,
        List<Condition> memoryConditions) {
        if (CollectionUtils.isEmpty(data)) {
            log.debug("输入数据为空，返回空列表");
            return data;
        }

        if (CollectionUtils.isEmpty(memoryConditions)) {
            log.debug("内存条件为空，返回原始数据");
            return data;
        }

        long startTime = System.currentTimeMillis();
        int originalSize = data.size();

        try {
            List<Map<String, Object>> filteredData = MemoryConditionEvaluator.evaluateConditions(
                data, memoryConditions);

            long duration = System.currentTimeMillis() - startTime;
            int filteredSize = filteredData.size();

            log.info("内存条件过滤完成，耗时: {}ms, 原始记录: {}, 过滤后记录: {}", duration, originalSize, filteredSize);

            return filteredData;
        } catch (Exception e) {
            log.error("内存条件过滤失败", e);
            throw new RuntimeException("内存条件过滤失败", e);
        }
    }

}
