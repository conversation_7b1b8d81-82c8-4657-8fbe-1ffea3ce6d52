package com.trs.moye.bi.engine.indicator.v1.indicator;

import com.trs.moye.base.data.indicator.enums.IndicatorStatOperation;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.springframework.stereotype.Component;

/**
 * 排名操作 基础代码实现
 */
@Component
public class RankOperation extends AbstractStatOperation {

    @Override
    public void calculate(List<Map<String, Object>> currentData, List<Map<String, Object>> historyData,
        IndicatorStatParams params) {
        String targetField = getTargetField(params);
        String resultField = getResultField(params);
        if (Objects.isNull(targetField) || Objects.isNull(resultField)) {
            return;
        }
        if (useHistory(params) && Objects.nonNull(historyData) && !historyData.isEmpty()) {
            calculateRank(historyData, targetField, resultField);
            copyHistoryResultToCurrent(currentData, historyData, params);
        } else {
            calculateRank(currentData, targetField, resultField);
        }
    }

    /**
     * 计算排名
     *
     * @param dataList    数据列表
     * @param targetField 目标字段
     * @param resultField result 字段
     * <AUTHOR>
     * @since 2025/05/28 14:20:35
     */
    private void calculateRank(List<Map<String, Object>> dataList, String targetField, String resultField) {
        // 创建带索引的数据列表用于排序
        List<IndexedValue> indexedValues = new ArrayList<>(dataList.size());
        for (int i = 0; i < dataList.size(); i++) {
            Number value = safeGetNumber(dataList.get(i), targetField);
            indexedValues.add(new IndexedValue(i, value));
        }

        // 对索引值对象进行排序（按targetField降序）
        indexedValues.sort(Comparator.comparing(iv -> iv.value,
            Comparator.nullsLast(Comparator.comparingDouble(Number::doubleValue).reversed())));

        // 分配排名
        int rank = 0;
        int position = 0;
        Number previousValue = null;
        Integer[] ranks = new Integer[dataList.size()];

        for (IndexedValue indexedValue : indexedValues) {
            position++;
            Number currentValue = indexedValue.value;

            if (Objects.isNull(currentValue)) {
                ranks[indexedValue.index] = null;
                continue;
            }

            // 如果当前值与前一个值不同，更新排名为当前位置
            if (Objects.isNull(previousValue) || !currentValue.equals(previousValue)) {
                rank = position;
            }

            ranks[indexedValue.index] = rank;
            previousValue = currentValue;
        }

        // 将排名赋值给原始数据
        for (int i = 0; i < dataList.size(); i++) {
            dataList.get(i).put(resultField, ranks[i]);
        }
    }

    // 辅助类，存储原始索引和对应的值
    private static class IndexedValue {

        final int index;
        final Number value;

        IndexedValue(int index, Number value) {
            this.index = index;
            this.value = value;
        }
    }

    @Override
    public boolean isSupport(IndicatorStatOperation operation) {
        return IndicatorStatOperation.RANK.equals(operation);
    }
}
