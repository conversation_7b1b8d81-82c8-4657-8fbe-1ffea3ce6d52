package com.trs.moye.bi.engine.indicator.v1.indicator;

import com.trs.moye.base.common.utils.DateTimeUtils;
import com.trs.moye.base.data.indicator.entity.StatisticPeriod;
import com.trs.moye.base.data.indicator.enums.IndicatorSearchRange;
import java.time.LocalDateTime;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

/**
 * 抽象StatOperation，提供常用辅助方法
 */
@Slf4j
public abstract class AbstractStatOperation implements StatOperation {

    protected String getTargetField(IndicatorStatParams params) {
        if (Objects.isNull(params) || Objects.isNull(params.getTargetField())) {
            return null;
        }
        return params.getTargetField().getEnName();
    }

    protected String getResultField(IndicatorStatParams params) {
        if (Objects.isNull(params) || Objects.isNull(params.getResultField())) {
            return null;
        }
        return params.getResultField().getEnName();
    }

    protected List<String> getStatisticDims(IndicatorStatParams params) {
        if (Objects.isNull(params) || Objects.isNull(params.getStatisticDims())) {
            return Collections.emptyList();
        }
        return params.getStatisticDims();
    }

    protected boolean useHistory(IndicatorStatParams params) {
        if (Objects.isNull(params) || Objects.isNull(params.getFieldConfig().getSearchRange())) {
            return false;
        }
        IndicatorSearchRange range = params.getFieldConfig().getSearchRange();
        return IndicatorSearchRange.LAST.equals(range)
            || IndicatorSearchRange.LAST_YEAR.equals(range)
            || IndicatorSearchRange.NEIGHBOR.equals(range)
            || IndicatorSearchRange.PREVIOUS.equals(range);
    }

    /**
     * 将历史数据计算结果复制到当前数据
     *
     * @param currentData 当前数据列表
     * @param historyData 历史数据列表
     * @param params      指标统计参数
     */
    protected void copyHistoryResultToCurrent(List<Map<String, Object>> currentData,
        List<Map<String, Object>> historyData,
        IndicatorStatParams params) {
        if (CollectionUtils.isEmpty(currentData)
            || CollectionUtils.isEmpty(historyData)
            || Objects.isNull(params)
            || Objects.isNull(params.getFieldConfig().getSearchRange())
            || Objects.isNull(params.getStartTimeField())) {
            return;
        }

        String resultField = getResultField(params);
        if (Objects.isNull(resultField)) {
            return;
        }
        String startTime = params.getStartTimeField();
        List<String> dims = getStatisticDims(params);
        HistoryDataGroup grouped = groupHistoryData(historyData, startTime, dims);
        for (Map<String, Object> cur : currentData) {
            LocalDateTime curTime = parseLocalDateTime(cur.get(startTime));
            if (Objects.isNull(curTime)) {
                continue;
            }

            LocalDateTime targetTs;
            switch (params.getFieldConfig().getSearchRange()) {
                case LAST ->
                    targetTs = StatisticPeriod.minusPeriod(params.getPeriodType(), params.getDiffPeriodNum(), curTime);
                case LAST_YEAR -> targetTs = curTime.minusYears(1);
                case PREVIOUS -> targetTs = StatisticPeriod.minusPeriod(params.getPeriodType(),
                    Long.valueOf(params.getFieldConfig().getPeriodIndex()), curTime);
                default -> targetTs = curTime;
            }
            Map<String, Object> hit = findMatchingHistoryItem(cur, grouped, dims, targetTs);
            if (Objects.nonNull(hit)) {
                cur.put(resultField, hit.get(resultField));
            }
        }
    }

    /**
     * 将历史数据按时间字段分组并缓存时间戳
     */
    protected static class HistoryDataGroup {

        final Map<LocalDateTime, Map<String, Map<String, Object>>> groupedData;

        HistoryDataGroup(Map<LocalDateTime, Map<String, Map<String, Object>>> groupedData) {
            this.groupedData = groupedData;
        }
    }

    protected HistoryDataGroup groupHistoryData(List<Map<String, Object>> historyData, String startTimeField,
        List<String> dims) {
        if (CollectionUtils.isEmpty(historyData)) {
            return new HistoryDataGroup(Collections.emptyMap());
        }
        return new HistoryDataGroup(historyData.stream()
            .map(item -> {
                LocalDateTime time = parseLocalDateTime(item.get(startTimeField));
                return Objects.nonNull(time) ? new AbstractMap.SimpleEntry<>(time, item) : null;
            })
            .filter(Objects::nonNull)
            .collect(Collectors.groupingBy(
                Map.Entry::getKey,
                Collectors.toMap(
                    entry -> buildDimKey(entry.getValue(), dims),
                    Map.Entry::getValue,
                    (existing, replacement) -> existing // In case of duplicate keys
                )
            ))
        );
    }

    protected String buildDimKey(Map<String, Object> item, List<String> dims) {
        if (CollectionUtils.isEmpty(dims)) {
            return "_"; // Default key for data with no dimensions
        }
        return dims.stream()
            .map(item::get)
            .map(String::valueOf)
            .collect(Collectors.joining("::"));
    }


    protected Map<String, Object> findMatchingHistoryItem(Map<String, Object> currentItem,
        HistoryDataGroup grouped, List<String> dims, LocalDateTime targetTs) {
        if (Objects.isNull(targetTs) || Objects.isNull(grouped.groupedData)) {
            return null;
        }
        Map<String, Map<String, Object>> itemsForTime = grouped.groupedData.get(targetTs);
        if (Objects.isNull(itemsForTime)) {
            return null;
        }
        String dimKey = buildDimKey(currentItem, dims);
        return itemsForTime.get(dimKey);
    }

    protected LocalDateTime parseLocalDateTime(Object timeObj) {
        if (Objects.isNull(timeObj)) {
            return null;
        }
        try {
            if (timeObj instanceof LocalDateTime localDateTime) {
                return localDateTime;
            } else {
                return DateTimeUtils.parse(String.valueOf(timeObj));
            }
        } catch (Exception e) {
            log.error("无法解析时间字段，值为: {}", timeObj, e);
            return null;
        }
    }

    // 去重并按时间排序的历史数据条目，过滤同维度并跳过当前时间点首条
    protected List<Map.Entry<LocalDateTime, Map<String, Object>>> buildTimedEntries(
        List<Map<String, Object>> history,
        Map<String, Object> current,
        List<String> dims,
        String timeField) {
        List<Map.Entry<LocalDateTime, Map<String, Object>>> timedEntries = new ArrayList<>();
        LocalDateTime currentLocalDateTime = parseLocalDateTime(current.get(timeField));
        boolean skippedCurrentTimeEntry = false;
        for (Map<String, Object> item : history) {
            boolean dimensionsMatch = dims.stream().allMatch(d -> Objects.equals(item.get(d), current.get(d)));
            if (dimensionsMatch) {
                LocalDateTime itemLocalDateTime = parseLocalDateTime(item.get(timeField));
                if (Objects.isNull(itemLocalDateTime)) {
                    log.warn("历史数据中时间字段 '{}' 的值为 null，跳过该条数据", timeField);
                    continue;
                }
                // 跳过当前时间点的数据
                if (!skippedCurrentTimeEntry && itemLocalDateTime.equals(currentLocalDateTime)) {
                    skippedCurrentTimeEntry = true;
                } else {
                    timedEntries.add(new AbstractMap.SimpleEntry<>(itemLocalDateTime, item));
                }
            }
        }
        timedEntries.sort(Map.Entry.comparingByKey());
        return timedEntries;
    }

    protected Number safeGetNumber(Map<String, Object> data, String field) {
        Object value = data.get(field);
        if (Objects.isNull(value)) {
            return null;
        }

        if (value instanceof Number number) {
            return number;
        } else if (value instanceof String stringValue) {
            try {
                return Double.parseDouble(stringValue);
            } catch (NumberFormatException e) {
                log.error("无法将字段 '{}' 的值 '{}' 转换为Number类型", field, stringValue, e);
                return null;
            }
        } else {
            log.error("字段 '{}' 的值类型 '{}' 不支持转换为Number", field, value.getClass().getName());
            return null;
        }
    }
}
