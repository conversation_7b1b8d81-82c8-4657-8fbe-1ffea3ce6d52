package com.trs.moye.bi.engine.indicator.v1.indicator;

import com.trs.moye.base.data.indicator.enums.IndicatorStatOperation;
import java.util.List;
import java.util.Map;

/**
 * stat作
 *
 * @since 2025/05/27 14:30:20
 */
public interface StatOperation {

    /**
     * 算
     *
     * @param currentData         数据
     * @param historyData         历史数据
     * @param indicatorStatParams stat 配置
     * @since 2025/05/27 15:20:25
     */
    void calculate(List<Map<String, Object>> currentData, List<Map<String, Object>> historyData,
        IndicatorStatParams indicatorStatParams);

    /**
     * 是否支持此操作类型
     *
     * @param operation 操作
     * @return boolean
     * <AUTHOR>
     * @since 2025/05/27 21:15:19
     */
    boolean isSupport(IndicatorStatOperation operation);
}

