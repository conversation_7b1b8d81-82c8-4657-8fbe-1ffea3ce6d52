package com.trs.moye.bi.engine.indicator;

import com.trs.moye.base.data.indicator.entity.IndicatorApplicationFieldConfig;
import com.trs.moye.bi.engine.indicator.entity.CalculationFunctionDefinition;
import com.trs.moye.bi.engine.indicator.function.CalculationFunctionRegistry;
import com.trs.moye.bi.engine.indicator.context.ParameterContext;
import com.trs.moye.bi.engine.indicator.function.ParameterResolver;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;

/**
 * 指标计算函数枚举
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
@Slf4j
public enum CalculationFunction {
    GET_FIELD_DATA("getFieldData"),
    GET_PREVIOUS_PERIOD_DATA("getPreviousPeriodData"),
    GET_PERIOD_OVER_PERIOD_DATA("getPeriodOverPeriodData"),
    GET_YEAR_OVER_YEAR_DATA("getYearOverYearData"),
    GET_RATIO_DATA("getRatioData"),
    GET_RANK_DATA("getRankData"),
    GET_YEAR_AGO_DATA("getYearAgoData"),
    GET_TREND_DATA("getTrendData"),
    GET_CONSECUTIVE_TREND_DATA("getConsecutiveTrendData"),
    GET_DATA_BY_SQL("getDataBySql"),
    GET_CONDITIONAL_DATA("getConditionalData"),
    IS_CONDITION_MET("isConditionMet"),
    COUNT_CONDITION_MET("countConditionMet"),
    ADD("add"),
    SUBTRACT("subtract"),
    MULTIPLY("multiply"),
    DIVIDE("divide"),
    HORIZONTAL_SUM("horizontalSum"),
    VERTICAL_SUM("verticalSum"),
    VERTICAL_SUM_EX("verticalSumEx"),
    CUMULATIVE_SUM("cumulativeSum"),
    DAILY_AVERAGE("dailyAverage"),
    GET_YOY_BY_FIELDS("getYoYByFields"),
    GENERATE_SEQUENCE("generateSequence");

    private final String functionName;

    CalculationFunction(String functionName) {
        this.functionName = functionName;
    }

    /**
     * 应用计算函数
     * <p>
     * 使用新的参数解析机制执行计算函数 支持传统的列表参数和新的命名参数两种方式
     * </p>
     *
     * @param config 配置
     * @return 计算结果
     * <AUTHOR>
     * @since 2025/07/29
     */
    public Map<String, Object> apply(IndicatorApplicationFieldConfig config) {
        try {
            // 获取函数定义
            Optional<CalculationFunctionDefinition> definitionOpt =
                CalculationFunctionRegistry.getDefinition(functionName);

            if (definitionOpt.isEmpty()) {
                log.warn("未找到函数定义: {}", functionName);
                return Collections.emptyMap();
            }

            CalculationFunctionDefinition definition = definitionOpt.get();

            // 解析参数
            ParameterContext parameterContext = ParameterResolver.resolve(definition, config);

            // 执行函数
            return definition.execute(parameterContext);

        } catch (Exception e) {
            log.error("计算函数 '{}' 执行失败，配置: {}", functionName, config, e);
            return Collections.emptyMap();
        }
    }

    /**
     * 发件人名称
     *
     * @param name 名字
     * @return {@link CalculationFunction }
     * <AUTHOR>
     * @since 2025/06/26 17:24:27
     */
    public static CalculationFunction fromName(String name) {
        return Stream.of(values())
            .filter(f -> f.functionName.equalsIgnoreCase(name) || f.name().equalsIgnoreCase(name))
            .findFirst()
            .orElse(null);
    }
}
