package com.trs.moye.bi.engine.indicator.annotation;

/**
 * 数据生成类型枚举
 * <p>
 * 用于标识指标函数的数据生成特性，帮助系统正确处理条件过滤逻辑
 * </p>
 *
 * <AUTHOR>
 * @since 2025/08/13
 */
public enum DataGenerationType {

    /**
     * 普通指标能力（默认值）
     * <p>
     * 不生成新数据，只是对现有数据进行计算或转换
     * 这类函数的结果可以在SQL层面进行条件过滤
     * </p>
     * <p>
     * 示例函数：
     * <ul>
     *   <li>getFieldData - 获取字段数据</li>
     *   <li>getPreviousPeriodData - 获取上期数据</li>
     *   <li>getYearOverYearData - 获取同比数据</li>
     * </ul>
     * </p>
     */
    NORMAL,

    /**
     * 数据生成器
     * <p>
     * 会创建新的字段和值，或者修改现有数据结构
     * 这类函数的结果必须在内存中进行条件过滤
     * </p>
     * <p>
     * 示例函数：
     * <ul>
     *   <li>verticalSumEx - 垂直求和扩展，会生成新的合计行</li>
     *   <li>horizontalSum - 水平求和，会生成新的计算字段</li>
     *   <li>generateSequence - 序号生成，会添加序号字段</li>
     *   <li>cumulativeSum - 累计求和，会生成累计值字段</li>
     * </ul>
     * </p>
     */
    DATA_GENERATOR
}
