package com.trs.moye.bi.engine.indicator.function.scanner;

import com.trs.moye.bi.engine.indicator.annotation.IndicatorCategory;
import com.trs.moye.bi.engine.indicator.annotation.IndicatorFunction;
import com.trs.moye.bi.engine.indicator.entity.CalculationFunctionDefinition;
import com.trs.moye.bi.engine.indicator.entity.CalculationParameter;
import com.trs.moye.bi.engine.indicator.context.ParameterContext;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;

/**
 * 指标计算函数注解扫描器
 * <p>
 * 负责扫描指定类中标记了 @IndicatorFunction 注解的方法， 解析方法的参数信息，生成对应的 CalculationFunctionDefinition
 * </p>
 *
 * <AUTHOR>
 * @since 2025/07/29
 */
@Slf4j
public class IndicatorFunctionScanner {

    private IndicatorFunctionScanner() {
    }

    private static final MethodScanner METHOD_SCANNER = new MethodScanner();
    private static final ParameterParser PARAMETER_PARSER = new ParameterParser();
    private static final ParameterProcessor PARAMETER_PROCESSOR = new ParameterProcessor();

    /**
     * 扫描指定类中的所有指标计算函数
     * <p>
     * 处理流程：
     * <ol>
     *   <li>遍历类中所有声明方法</li>
     *   <li>筛选带有 @IndicatorFunction 注解的方法</li>
     *   <li>按函数名称分组，处理方法重载</li>
     *   <li>合并重载方法为单一函数定义</li>
     *   <li>解析参数构建参数定义列表</li>
     *   <li>创建智能函数执行器</li>
     *   <li>构建完整的函数定义</li>
     * </ol>
     * </p>
     *
     * @param clazz 要扫描的目标类
     * @return 解析得到的函数定义列表
     */
    public static List<CalculationFunctionDefinition> scanClass(Class<?> clazz) {
        List<CalculationFunctionDefinition> definitions = new ArrayList<>();

        // 获取类上的分类注解
        IndicatorCategory categoryAnnotation = clazz.getAnnotation(IndicatorCategory.class);
        String defaultCategory = categoryAnnotation != null ? categoryAnnotation.name() : "默认分类";

        log.debug("开始扫描类: {}, 默认分类: {}", clazz.getSimpleName(), defaultCategory);

        // 按函数名称分组处理方法重载
        Map<String, List<Method>> methodGroups = METHOD_SCANNER.scanAnnotatedMethods(clazz);

        for (Map.Entry<String, List<Method>> entry : methodGroups.entrySet()) {
            String functionName = entry.getKey();
            List<Method> overloadedMethods = entry.getValue();

            try {
                CalculationFunctionDefinition definition = createMergedFunctionDefinition(
                    functionName, overloadedMethods, defaultCategory);
                definitions.add(definition);

                log.debug("成功扫描函数: {} ({} 个重载方法)", functionName, overloadedMethods.size());

            } catch (Exception e) {
                log.error("扫描函数失败: {}", functionName, e);
            }
        }

        log.info("类 {} 扫描完成，共发现 {} 个函数", clazz.getSimpleName(), definitions.size());
        return definitions;
    }

    /**
     * 创建合并的函数定义
     *
     * @param functionName      函数名称
     * @param overloadedMethods 重载方法列表
     * @param defaultCategory   默认分类
     * @return 合并后的函数定义
     */
    private static CalculationFunctionDefinition createMergedFunctionDefinition(
        String functionName, List<Method> overloadedMethods, String defaultCategory) {

        // 选择参数最多的方法作为主方法（包含所有可能的参数）
        Method primaryMethod = overloadedMethods.stream()
            .max((m1, m2) -> Integer.compare(m1.getParameterCount(), m2.getParameterCount()))
            .orElseThrow(() -> new IllegalArgumentException("没有找到有效的方法"));

        IndicatorFunction functionAnnotation = primaryMethod.getAnnotation(IndicatorFunction.class);

        // 解析合并后的参数定义
        List<CalculationParameter> parameters = PARAMETER_PARSER.parseMergedParameters(overloadedMethods);

        // 创建智能函数执行器
        Function<ParameterContext, Map<String, Object>> executor = createSmartExecutor(overloadedMethods);

        // 构建函数定义
        return CalculationFunctionDefinition.builder()
            .functionName(functionName)
            .description(functionAnnotation.description())
            .category(defaultCategory)
            .parameters(parameters)
            .executor(executor)
            .internal(false)
            .primaryMethod(primaryMethod)  // 保存主要方法对象
            .build();
    }

    /**
     * 创建智能函数执行器（处理方法重载）
     * <p>
     * 根据提供的参数数量，智能选择合适的重载方法进行调用
     * </p>
     *
     * @param overloadedMethods 重载方法列表
     * @return 智能函数执行器
     */
    private static Function<ParameterContext, Map<String, Object>> createSmartExecutor(List<Method> overloadedMethods) {
        return context -> {
            try {
                // 根据提供的参数数量选择合适的方法
                Method selectedMethod = selectMethodByParameterCount(overloadedMethods, context);

                // 准备方法调用参数
                Object[] args = PARAMETER_PROCESSOR.prepareMethodArguments(selectedMethod, context);

                // 调用选中的方法
                Map<String, Object> result = (Map<String, Object>) selectedMethod.invoke(null, args);

                return result != null ? result : new HashMap<>();

            } catch (Exception e) {
                log.error("执行智能函数失败", e);
                return new HashMap<>();
            }
        };
    }

    /**
     * 根据参数数量选择合适的重载方法
     * <p>
     * 智能选择策略：
     * <ol>
     *   <li>优先检查是否为传统 List&lt;String&gt; 参数调用</li>
     *   <li>对于传统参数，直接根据列表长度选择匹配的方法</li>
     *   <li>对于命名参数，计算实际提供的非空参数数量</li>
     *   <li>选择参数数量最匹配的方法</li>
     * </ol>
     * </p>
     *
     * @param overloadedMethods 重载方法列表
     * @param context           参数上下文
     * @return 选中的方法
     */
    private static Method selectMethodByParameterCount(List<Method> overloadedMethods, ParameterContext context) {
        // 按参数数量排序重载方法，便于后续处理
        List<Method> sortedMethods = overloadedMethods.stream()
            .sorted(Comparator.comparingInt(Method::getParameterCount))
            .toList();

        // 优先检查是否为传统列表参数调用
        Object legacyInputList = context.getRawConfig().get("input");
        if (legacyInputList instanceof List) {
            List<String> inputList = (List<String>) legacyInputList;
            int legacyParamCount = inputList.size();

            log.debug("传统列表参数调用，参数数量: {}，可用重载方法: {}",
                legacyParamCount,
                sortedMethods.stream().map(Method::getParameterCount).toList());

            // 精确匹配：优先选择参数数量完全匹配的方法
            Optional<Method> exactMatch = sortedMethods.stream()
                .filter(method -> method.getParameterCount() == legacyParamCount)
                .findFirst();

            if (exactMatch.isPresent()) {
                log.debug("找到精确匹配的方法，参数数量: {}", legacyParamCount);
                return exactMatch.get();
            }

            // 如果没有精确匹配，选择参数数量最接近且不超过提供参数数量的方法
            Optional<Method> bestMatch = sortedMethods.stream()
                .filter(method -> method.getParameterCount() <= legacyParamCount)
                .max((m1, m2) -> Integer.compare(m1.getParameterCount(), m2.getParameterCount()));

            if (bestMatch.isPresent()) {
                log.debug("选择最佳匹配方法，参数数量: {} (提供: {})",
                    bestMatch.get().getParameterCount(), legacyParamCount);
                return bestMatch.get();
            }

            // 如果所有方法的参数都比提供的多，选择参数最少的方法
            log.debug("所有方法参数都比提供的多，选择参数最少的方法: {}",
                sortedMethods.get(0).getParameterCount());
            return sortedMethods.get(0);
        }

        // 对于命名参数，计算实际提供的非空参数数量
        int providedParamCount = (int) context.getParameterNames().stream()
            .map(context::getObject)
            .filter(value -> value != null && !isEmptyValue(value))
            .count();

        log.debug("命名参数调用，提供的非空参数数量: {}，可用重载方法: {}",
            providedParamCount,
            sortedMethods.stream().map(Method::getParameterCount).toList());

        // 对于命名参数，选择能够处理提供参数数量的最合适方法
        // 策略：选择参数数量大于等于提供参数数量的最小方法
        Optional<Method> suitableMethod = sortedMethods.stream()
            .filter(method -> method.getParameterCount() >= providedParamCount)
            .findFirst();

        if (suitableMethod.isPresent()) {
            log.debug("选择合适的方法，参数数量: {} (提供: {})",
                suitableMethod.get().getParameterCount(), providedParamCount);
            return suitableMethod.get();
        }

        // 如果没有找到合适的方法，选择参数最多的方法（可能有默认值）
        Method maxParamMethod = sortedMethods.get(sortedMethods.size() - 1);
        log.debug("选择参数最多的方法作为默认选择，参数数量: {}", maxParamMethod.getParameterCount());
        return maxParamMethod;
    }

    /**
     * 判断值是否为空
     *
     * @param value 要判断的值
     * @return 如果为空返回true
     */
    private static boolean isEmptyValue(Object value) {
        if (value == null) {
            return true;
        }
        if (value instanceof String s) {
            return s.trim().isEmpty();
        }
        return false;
    }

}
