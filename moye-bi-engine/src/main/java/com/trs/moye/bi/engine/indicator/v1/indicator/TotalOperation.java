package com.trs.moye.bi.engine.indicator.v1.indicator;

import com.trs.moye.base.data.indicator.enums.IndicatorStatOperation;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * 总数操作 基础代码实现
 */
@Component
public class TotalOperation extends AbstractStatOperation {

    @Override
    public void calculate(List<Map<String, Object>> currentData, List<Map<String, Object>> historyData,
        IndicatorStatParams params) {
        String targetField = getTargetField(params);
        String resultField = getResultField(params);
        if (Objects.isNull(targetField) || Objects.isNull(resultField)) {
            return;
        }
        if (useHistory(params)) {
            if (CollectionUtils.isEmpty(historyData)) {
                return;
            }
            // 先在历史数据中填充结果字段
            historyData.forEach(e -> e.put(resultField, e.get(targetField)));
            // 将历史结果复制到当前数据
            copyHistoryResultToCurrent(currentData, historyData, params);
        } else {
            // 使用当前数据
            for (Map<String, Object> data : currentData) {
                data.put(resultField, data.get(targetField));
            }
        }
    }

    @Override
    public boolean isSupport(IndicatorStatOperation operation) {
        return IndicatorStatOperation.TOTAL.equals(operation);
    }

}
