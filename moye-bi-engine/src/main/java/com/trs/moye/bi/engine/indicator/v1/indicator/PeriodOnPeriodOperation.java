package com.trs.moye.bi.engine.indicator.v1.indicator;

import com.trs.moye.base.data.indicator.entity.StatisticPeriod;
import com.trs.moye.base.data.indicator.enums.IndicatorStatOperation;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * 环比操作
 *
 * <AUTHOR>
 * @since 2025/05/27 15:35:22
 */
@Component
@Slf4j
public class PeriodOnPeriodOperation extends AbstractStatOperation {

    @Override
    public void calculate(List<Map<String, Object>> currentData, List<Map<String, Object>> historyData,
        IndicatorStatParams params) {

        String targetField = getTargetField(params);
        String resultField = getResultField(params);
        if (Objects.isNull(targetField) || Objects.isNull(resultField) || CollectionUtils.isEmpty(historyData)) {
            return;
        }
        List<String> statisticDims = getStatisticDims(params);
        String startField = params.getStartTimeField();
        if (Objects.isNull(startField)) {
            return;
        }

        // 预处理历史数据
        HistoryDataGroup groupedHistory = groupHistoryData(historyData, startField, statisticDims);
        IndicatorStatOperation operation = params.getFieldConfig().getOperation();

        for (Map<String, Object> data : currentData) {
            Number count = safeGetNumber(data, targetField);
            if (Objects.isNull(count)) {
                continue;
            }

            LocalDateTime curTime = parseLocalDateTime(data.get(startField));
            if (Objects.isNull(curTime)) {
                continue;
            }

            LocalDateTime targetTs;
            if (IndicatorStatOperation.PERIOD_ON_PERIOD.equals(operation)) {
                targetTs = StatisticPeriod.minusPeriod(params.getPeriodType(), params.getDiffPeriodNum(), curTime);
            } else if (IndicatorStatOperation.YEAR_ON_YEAR.equals(operation)) {
                targetTs = curTime.minusYears(1);
            } else {
                continue;
            }

            Map<String, Object> hist = findMatchingHistoryItem(data, groupedHistory, statisticDims, targetTs);
            if (Objects.nonNull(hist)) {
                Number histValue = safeGetNumber(hist, targetField);
                String result = calculateSafeRatio(count, histValue);
                data.put(resultField, result);
            }
        }
    }


    @Override
    public boolean isSupport(IndicatorStatOperation operation) {
        return IndicatorStatOperation.PERIOD_ON_PERIOD.equals(operation)
            || IndicatorStatOperation.YEAR_ON_YEAR.equals(operation);
    }

    private String calculateSafeRatio(Number current, Number history) {
        if (Objects.isNull(history) || history.doubleValue() == 0) {
            return "";
        }
        double ratio = (current.doubleValue() - history.doubleValue()) * 100.0 / history.doubleValue();
        return String.format("%.2f%%", ratio);
    }

}
