package com.trs.moye.bi.engine.indicator.v1;

import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.data.indicator.entity.IndicatorDataSearchParams;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;

/**
 * 查询db服务类
 */
@Service
public interface IndicatorService {


    /**
     * 指标查询
     *
     * @param connectionId 连接id
     * @param request      请求
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @since 2025/05/27 21:36:14
     */
    PageResponse<Map<String, Object>> indicatorQuery(Integer connectionId, IndicatorDataSearchParams request);
}

