package com.trs.moye.bi.engine.indicator.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 指标计算函数参数注解
 * <p>
 * 用于标注指标计算函数方法的参数信息，包括参数名称、描述、是否必需、默认值等
 * </p>
 *
 * <AUTHOR>
 * @since 2025/07/29
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.PARAMETER})
public @interface IndicatorParameter {

    /**
     * 参数名称
     * <p>
     * 用于命名参数配置中的键名，应该具有明确的语义 例如：targetField, operator, comparisonValue
     * </p>
     *
     * @return 参数名称
     */
    String name();

    /**
     * 参数描述
     * <p>
     * 用于界面显示和文档生成，描述参数的用途和取值范围
     * </p>
     *
     * @return 参数描述
     */
    String description();

    /**
     * 参数是否必需
     * <p>
     * true: 必需参数，缺失时会抛出异常 false: 可选参数，缺失时使用默认值 对于必需参数，可以省略此属性（默认为true）
     * </p>
     *
     * @return 是否必需，默认为true
     */
    boolean required() default true;

    /**
     * 默认值
     * <p>
     * 当参数为可选且未提供时使用的默认值 以字符串形式提供，会根据参数类型进行转换
     * </p>
     *
     * @return 默认值，默认为空字符串
     */
    String defaultValue() default "";
}
