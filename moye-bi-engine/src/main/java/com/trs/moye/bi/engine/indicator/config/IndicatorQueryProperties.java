package com.trs.moye.bi.engine.indicator.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 指标查询配置属性
 * <p>
 * 用于配置指标查询相关的功能开关和参数
 *
 * <AUTHOR>
 * @since 2025/07/24
 */
@Data
@Component
@ConfigurationProperties(prefix = "indicator.query")
public class IndicatorQueryProperties {

    /**
     * 是否启用去年整年数据获取功能
     * <p>
     * 当设置为 true 时，系统会在历史数据查询中额外获取 相对于当前统计周期的去年一整年的完整历史数据
     * <p>
     */
    private boolean enableLastYearFullData = false;

}
