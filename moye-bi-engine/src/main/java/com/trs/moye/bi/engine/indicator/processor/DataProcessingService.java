package com.trs.moye.bi.engine.indicator.processor;

import com.trs.moye.base.common.utils.DateTimeUtils;
import com.trs.moye.base.data.indicator.entity.DataModelIndicatorField;
import com.trs.moye.base.data.indicator.entity.IndicatorApplicationFieldConfig;
import com.trs.moye.base.data.indicator.entity.IndicatorSortField;
import com.trs.moye.base.data.indicator.entity.StatisticPeriod;
import com.trs.moye.base.data.indicator.enums.IndicatorSortOrder;
import com.trs.moye.bi.engine.indicator.CalculationFunction;
import com.trs.moye.bi.engine.indicator.function.calculation.SequenceGeneration;
import com.trs.moye.bi.engine.indicator.context.QueryContext;
import com.trs.moye.bi.engine.indicator.exception.IndicatorQueryException;
import com.trs.moye.bi.engine.indicator.util.IndicatorDataSorter;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 数据处理服务
 * <p>
 * 负责对查询结果数据进行各种后处理操作，包括：
 * <ul>
 *   <li>数据排序：支持内存排序和数据库排序</li>
 *   <li>目标周期筛选：根据指定的时间周期过滤数据</li>
 *   <li>返回字段过滤：只保留指定的字段</li>
 * </ul>
 * </p>
 *
 * <AUTHOR>
 * @since 2025/07/28
 */
@Slf4j
@Service
public class DataProcessingService {

    /**
     * 应用排序
     * <p>
     * 排序策略：
     * <ul>
     *   <li>固定排序（FIXED）：无论字段类型如何，都必须在内存中进行排序，因为数据库无法处理固定顺序排序</li>
     *   <li>应用字段排序：计算得出的字段必须在内存中排序，因为数据库中不存在这些字段</li>
     *   <li>元数据字段的ASC/DESC排序：优先使用数据库排序（性能更好）</li>
     * </ul>
     * </p>
     *
     * @param data    待排序的数据列表
     * @param context 查询上下文，包含排序配置
     * @return 排序后的数据列表
     * @throws IndicatorQueryException 当应用排序失败时抛出
     */
    public List<Map<String, Object>> applySorting(List<Map<String, Object>> data, QueryContext context) {
        IndicatorSortField sortField = context.getRequest().getSortField();
        if (sortField == null) {
            return data;
        }

        try {
            log.debug("开始应用排序, 排序字段: {}, 排序类型: {}",
                sortField.getField(), sortField.getOrder());

            // 判断是否需要内存排序
            boolean needsMemorySorting = shouldUseMemorySorting(sortField, context);

            if (needsMemorySorting) {
                List<Map<String, Object>> sortedData = IndicatorDataSorter.sort(data, sortField);
                log.debug("内存排序完成, 排序字段: {}, 排序类型: {}, 数据量: {}",
                    sortField.getField(), sortField.getOrder(), sortedData.size());
                return sortedData;
            }

            // 使用数据库排序结果
            log.debug("使用数据库排序结果, 排序字段: {}, 排序类型: {}",
                sortField.getField(), sortField.getOrder());
            return data;

        } catch (Exception e) {
            log.error("应用排序失败, 排序字段: {}, 排序类型: {}",
                sortField.getField(), sortField.getOrder(), e);
            throw IndicatorQueryException.dataProcessingError("应用排序", e);
        }
    }

    /**
     * 判断是否需要使用内存排序
     * <p>
     * 内存排序的条件：
     * <ul>
     *   <li>固定排序（FIXED）：数据库无法处理固定顺序，必须内存排序</li>
     *   <li>应用字段排序：计算字段在数据库中不存在，必须内存排序</li>
     * </ul>
     * </p>
     *
     * @param sortField 排序字段配置
     * @param context   查询上下文
     * @return true表示需要内存排序，false表示可以使用数据库排序
     */
    private boolean shouldUseMemorySorting(IndicatorSortField sortField, QueryContext context) {
        // 1. 固定排序必须在内存中进行
        if (sortField.getOrder() == IndicatorSortOrder.FIXED) {
            log.debug("固定排序必须使用内存排序, 排序字段: {}", sortField.getField());
            return true;
        }

        // 2. 应用字段（计算字段）必须在内存中排序
        List<String> applicationFields = context.getFieldGroups().getApplicationFieldNames();
        if (applicationFields.contains(sortField.getField())) {
            log.debug("应用字段必须使用内存排序, 排序字段: {}", sortField.getField());
            return true;
        }

        // 3. 元数据字段的ASC/DESC排序可以使用数据库排序
        log.debug("元数据字段的ASC/DESC排序使用数据库排序, 排序字段: {}", sortField.getField());
        return false;
    }

    /**
     * 按目标周期筛选结果
     * <p>
     * 根据指定的目标时间周期列表，过滤出在这些时间范围内的数据记录 筛选逻辑：数据记录的开始时间必须在目标周期的时间范围内
     * </p>
     *
     * @param data    待筛选的数据列表
     * @param context 查询上下文，包含目标周期配置
     * @return 筛选后的数据列表
     * @throws IndicatorQueryException 当按目标周期筛选失败时抛出
     */
    public List<Map<String, Object>> filterByTargetPeriods(List<Map<String, Object>> data, QueryContext context) {
        List<StatisticPeriod> targetPeriods = context.getRequest().getTargetPeriods();
        String startTimeKey = context.getFieldGroups().startTimeField();

        if (CollectionUtils.isEmpty(targetPeriods) || StringUtils.isBlank(startTimeKey)) {
            return data;
        }

        try {
            log.debug("开始按目标周期筛选, 目标周期数量: {}", targetPeriods.size());

            List<Map<String, Object>> filteredData = data.stream().filter(item -> {
                Object timeValue = item.get(startTimeKey);
                if (timeValue == null) {
                    return false;
                }
                try {
                    LocalDateTime itemStartTime;
                    if (timeValue instanceof LocalDateTime time) {
                        itemStartTime = time;
                    } else {
                        itemStartTime = DateTimeUtils.parse(timeValue.toString());
                    }
                    // 检查数据时间是否在任一目标周期范围内
                    return targetPeriods.stream().anyMatch(
                        period -> !itemStartTime.isBefore(period.getStartTime())
                            && itemStartTime.isBefore(period.getEndTime()));
                } catch (Exception e) {
                    log.warn("无法解析时间戳: {}", timeValue, e);
                    return false;
                }
            }).collect(Collectors.toCollection(ArrayList::new));

            log.debug("目标周期筛选完成, 筛选后数据量: {}", filteredData.size());
            return filteredData;

        } catch (Exception e) {
            log.error("按目标周期筛选失败", e);
            throw IndicatorQueryException.dataProcessingError("目标周期筛选", e);
        }
    }

    /**
     * 过滤返回字段
     * <p>
     * 根据指定的返回字段列表，从每行数据中只保留这些字段，过滤掉其他字段 如果返回字段列表为空，则返回原始数据（包含所有字段）
     * </p>
     *
     * @param data         原始数据列表
     * @param returnFields 需要返回的字段名列表
     * @return 过滤后的数据列表，每行只包含指定的字段
     * @throws IndicatorQueryException 当过滤返回字段失败时抛出
     */
    public List<Map<String, Object>> filterReturnFields(List<Map<String, Object>> data, List<String> returnFields) {
        if (returnFields == null || returnFields.isEmpty()) {
            return data;
        }

        try {
            log.debug("开始过滤返回字段, 返回字段数量: {}", returnFields.size());

            List<Map<String, Object>> filteredData = data.stream().map(row -> {
                Map<String, Object> filteredRow = new HashMap<>();
                // 只保留指定的返回字段
                returnFields.forEach(field -> {
                    if (row.containsKey(field)) {
                        filteredRow.put(field, row.get(field));
                    }
                });
                return filteredRow;
            }).toList();

            log.debug("返回字段过滤完成");
            return filteredData;

        } catch (Exception e) {
            log.error("过滤返回字段失败", e);
            throw IndicatorQueryException.dataProcessingError("过滤返回字段", e);
        }
    }

    /**
     * 生成序号字段
     * <p>
     * 检查应用字段配置中是否有使用 GENERATE_SEQUENCE 函数的字段 为这些字段在最终数据中添加从1开始的递增序号 序号基于数据在列表中的实际顺序生成，确保与最终排序结果一致
     * </p>
     *
     * @param data    待添加序号的数据列表
     * @param context 查询上下文，包含字段配置信息
     * @return 添加了序号字段的数据列表
     * @throws IndicatorQueryException 当序号生成失败时抛出
     */
    public List<Map<String, Object>> generateSequenceNumbers(List<Map<String, Object>> data, QueryContext context) {
        if (data == null || data.isEmpty()) {
            return data;
        }

        try {
            log.debug("开始检查序号生成需求");

            // 查找需要生成序号的应用字段
            List<String> sequenceFields = findSequenceFields(context);

            if (sequenceFields.isEmpty()) {
                log.debug("未发现需要生成序号的字段");
                return data;
            }

            log.debug("发现需要生成序号的字段: {}", sequenceFields);

            // 为数据生成序号
            List<Map<String, Object>> result = SequenceGeneration.addMultipleSequencesToData(data, sequenceFields);

            log.debug("序号生成完成, 序号字段数量: {}, 数据量: {}", sequenceFields.size(), result.size());
            return result;

        } catch (Exception e) {
            log.error("序号生成失败", e);
            throw IndicatorQueryException.dataProcessingError("序号生成", e);
        }
    }

    /**
     * 查找需要生成序号的应用字段
     * <p>
     * 遍历所有启用的应用字段，查找配置了 GENERATE_SEQUENCE 计算函数的字段 返回这些字段的英文名称列表，用于后续的序号生成
     * </p>
     *
     * @param context 查询上下文，包含字段配置信息
     * @return 需要生成序号的字段名称列表
     */
    private List<String> findSequenceFields(QueryContext context) {
        return context.getFieldGroups().applicationFields().stream()
            .filter(DataModelIndicatorField::isEnable)
            .filter(field -> field.getConfig() instanceof IndicatorApplicationFieldConfig)
            .filter(this::isSequenceGenerationField)
            .map(DataModelIndicatorField::getEnName)
            .toList();
    }

    /**
     * 判断字段是否为序号生成字段
     * <p>
     * 检查字段配置中的计算函数是否为 GENERATE_SEQUENCE
     * </p>
     *
     * @param field 待检查的应用字段
     * @return 如果是序号生成字段返回true，否则返回false
     */
    private boolean isSequenceGenerationField(DataModelIndicatorField field) {
        try {
            IndicatorApplicationFieldConfig config = (IndicatorApplicationFieldConfig) field.getConfig();
            String functionName = config.getFunction();

            if (StringUtils.isBlank(functionName)) {
                return false;
            }

            CalculationFunction calculationFunction = CalculationFunction.fromName(functionName);
            return calculationFunction == CalculationFunction.GENERATE_SEQUENCE;
        } catch (Exception e) {
            log.warn("检查序号生成字段时发生异常, fieldName: {}", field.getEnName(), e);
            return false;
        }
    }
}
