package com.trs.moye.bi.engine.common.config;

import com.trs.moye.base.common.log.api.ApiLogTracerUtils;
import com.trs.moye.base.common.utils.JsonUtils;
import feign.Request;
import feign.RequestInterceptor;
import feign.codec.Decoder;
import feign.codec.Encoder;
import java.util.concurrent.TimeUnit;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.ResponseEntityDecoder;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;

/**
 * Feign配置 采用okhttp3客户端替换默认的httpclient
 */
@Setter
@Configuration
@Slf4j
public class OpenFeignConfig {

    @Value("${feign.request.timeout-minutes:30}")
    private int timeoutMinutes = 30;

    /**
     * open feign okhttp3 client
     *
     * @return {@link OkHttpClient}
     */
    @Bean
    public OkHttpClient openFeignOkhttp3Client() {
        return new OkHttpClient.Builder()
            //是否开启缓存
            .retryOnConnectionFailure(true)
            //连接池
            .connectionPool(new ConnectionPool(200, 5, TimeUnit.MINUTES))
            // 连接超时时间
            .connectTimeout(timeoutMinutes, TimeUnit.MINUTES)
            // 读取超时时间
            .readTimeout(timeoutMinutes, TimeUnit.MINUTES)
            .build();
    }

    /**
     * decoder
     *
     * @return {@link Decoder}
     */
    @Bean
    public Decoder feignDecoder() {
        MappingJackson2HttpMessageConverter jacksonConverter = new MappingJackson2HttpMessageConverter(
            JsonUtils.OBJECT_MAPPER);
        ObjectFactory<HttpMessageConverters> objectFactory = () -> new HttpMessageConverters(jacksonConverter);
        return new ResponseEntityDecoder(new SpringDecoder(objectFactory));
    }

    /**
     * encoder
     *
     * @return {@link Encoder}
     */
    @Bean
    public Encoder feignEncoder() {
        MappingJackson2HttpMessageConverter jacksonConverter = new MappingJackson2HttpMessageConverter(
            JsonUtils.OBJECT_MAPPER);
        ObjectFactory<HttpMessageConverters> objectFactory = () -> new HttpMessageConverters(jacksonConverter);
        return new SpringEncoder(objectFactory);
    }

    /**
     * open feign
     *
     * @return {@link RequestInterceptor}
     */
    @Bean
    public RequestInterceptor openFeignOkhttp3RequestInterceptor() {
        return requestTemplate -> {
            String url = requestTemplate.feignTarget().url() + requestTemplate.url();
            log.info("OkHttp ----- url: {} method: {} params: {}", url,
                requestTemplate.method(), requestTemplate.queryLine());
            byte[] body = requestTemplate.body();
            // 对于只有路径参数的请求，body为空
            log.info("OkHttp ----- body:\n{}", body == null ? null : new String(requestTemplate.body()));
            // 设置日志追踪ID，便于链路跟踪
            Long logId = ApiLogTracerUtils.getLogId();
            if (logId != null) {
                ApiLogTracerUtils.appendInputDetail("远程调用地址", url);
                ApiLogTracerUtils.stop();
                requestTemplate.header(ApiLogTracerUtils.LOG_ID_HEADER, logId.toString());
            }
        };
    }

    /**
     * 添加请求超时设置
     *
     * @return {@link Request.Options} 请求超时设置
     */
    @Bean
    public Request.Options openFeignOkhttp3RequestOptions() {
        return new Request.Options(timeoutMinutes, TimeUnit.MINUTES, timeoutMinutes, TimeUnit.MINUTES, true);
    }
}
