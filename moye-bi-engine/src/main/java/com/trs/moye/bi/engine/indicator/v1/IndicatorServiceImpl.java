package com.trs.moye.bi.engine.indicator.v1;

import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.common.utils.DateTimeUtils;
import com.trs.moye.base.data.connection.dao.DataConnectionMapper;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.indicator.dao.IndicatorConfigMapper;
import com.trs.moye.base.data.indicator.dao.IndicatorFieldMapper;
import com.trs.moye.base.data.indicator.dao.IndicatorPeriodConfigMapper;
import com.trs.moye.base.data.indicator.entity.DataModelIndicatorField;
import com.trs.moye.base.data.indicator.entity.DataModelIndicatorFieldEntity;
import com.trs.moye.base.data.indicator.entity.IndicatorApplicationFieldConfig;
import com.trs.moye.base.data.indicator.entity.IndicatorConfig;
import com.trs.moye.base.data.indicator.entity.IndicatorDataSearchParams;
import com.trs.moye.base.data.indicator.entity.IndicatorMetaFieldConfig;
import com.trs.moye.base.data.indicator.entity.IndicatorPeriodConfigEntity;
import com.trs.moye.base.data.indicator.entity.IndicatorSortField;
import com.trs.moye.base.data.indicator.entity.StatisticPeriod;
import com.trs.moye.base.data.indicator.enums.IndicatorFieldType;
import com.trs.moye.base.data.indicator.enums.IndicatorPeriodType;
import com.trs.moye.base.data.indicator.enums.IndicatorSearchRange;
import com.trs.moye.base.data.indicator.enums.IndicatorSortOrder;
import com.trs.moye.base.data.indicator.enums.IndicatorStatOperation;
import com.trs.moye.base.data.indicator.utils.IndicatorPeriodConverter;
import com.trs.moye.base.data.model.dao.DataModelFieldMapper;
import com.trs.moye.base.data.model.entity.AbstractField;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.bi.engine.common.request.CodeSearchParams;
import com.trs.moye.bi.engine.feign.SearchFeign;
import com.trs.moye.bi.engine.indicator.IndicatorServiceNew.FieldGroups;
import com.trs.moye.bi.engine.indicator.util.IndicatorQueryBuilder;
import com.trs.moye.bi.engine.indicator.v1.indicator.IndicatorStatParams;
import com.trs.moye.bi.engine.indicator.v1.indicator.StatOperationFactory;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 重构后的指标服务 优化点： 1. 提取公共查询逻辑到统一方法 2. 使用策略模式处理不同查询类型 3. 减少方法长度，提取辅助方法 4. 使用函数式接口减少重复代码
 */
@Service
@Slf4j
public class IndicatorServiceImpl implements IndicatorService {

    @Resource
    private DataConnectionMapper dataConnectionMapper;
    @Resource
    private IndicatorConfigMapper indicatorConfigMapper;
    @Resource
    private IndicatorPeriodConfigMapper indicatorPeriodConfigMapper;
    @Resource
    private IndicatorFieldMapper indicatorFieldMapper;
    @Resource
    private DataModelFieldMapper dataModelFieldMapper;
    @Resource
    private StatOperationFactory statOperationFactory;
    @Resource
    private SearchFeign searchFeign;


    @Override
    public PageResponse<Map<String, Object>> indicatorQuery(Integer connectionId, IndicatorDataSearchParams request) {
        DataConnection dataConnection = dataConnectionMapper.selectById(connectionId);
        if (dataConnection == null) {
            throw new BizException("未找到数据连接");
        }
        return handleDataQuery(dataConnection, request);
    }

    private PageResponse<Map<String, Object>> handleDataQuery(DataConnection dataConnection,
        IndicatorDataSearchParams request) {
        try {
            // 获取指标配置
            IndicatorConfig indicatorConfig = getIndicatorConfig(request.getDataModelId());

            // 获取字段信息
            FieldGroups fieldGroups = getFieldGroups(request.getDataModelId());

            // 构建查询SQL
            StatisticPeriod statisticPeriod = request.getStatisticPeriod();
            if (request.getSearchRange() != null) {
                statisticPeriod = calculateCurrentPeriod(indicatorConfig, request.getSearchRange());
            }
            // 执行查询
            IndicatorQueryBuilder queryBuilder = new IndicatorQueryBuilder(request, fieldGroups, dataConnection);
            String sql = queryBuilder.buildConditionalQuerySql();
            List<Map<String, Object>> currentData = executeQuery(dataConnection.getId(), sql);

            // 处理统计计算
            processStatistics(currentData, fieldGroups, indicatorConfig, request.getTableName(), dataConnection,
                request,
                statisticPeriod);

            // 处理排序
            if (request.getSortField() != null) {
                currentData = sortData(currentData, request.getSortField(), fieldGroups.getApplicationFieldNames());
            }
            List<Map<String, Object>> maps = filterReturnFields(currentData, request.getReturnFields());
            // 过滤返回字段
            return new PageResponse<>(maps, maps.size());
        } catch (Exception e) {
            throw new BizException("查询指标数据失败", e);
        }
    }

    /**
     * 核心统计处理逻辑
     *
     * @param currentData     当前数据
     * @param fieldGroups     字段组
     * @param indicatorConfig 指标配置
     * @param tableName       表名称
     * @param dataConnection  数据连接
     * @param request         请求
     * @param currentPeriod   当前期间
     * <AUTHOR>
     * @since 2025/06/03 20:11:13
     */
    private void processStatistics(List<Map<String, Object>> currentData, FieldGroups fieldGroups,
        IndicatorConfig indicatorConfig, String tableName, DataConnection dataConnection,
        IndicatorDataSearchParams request,
        StatisticPeriod currentPeriod) {

        // 构建统计参数
        IndicatorStatParams statParams = buildStatParams(indicatorConfig, fieldGroups, currentPeriod);

        // 获取历史数据
        Map<IndicatorSearchRange, List<Map<String, Object>>> historicalData = getHistoricalData(dataConnection,
            request, tableName,
            fieldGroups, statParams, currentPeriod);

        // 应用字段计算
        applyFieldCalculations(currentData, fieldGroups, statParams, historicalData);
    }

    // 辅助方法集
    private IndicatorConfig getIndicatorConfig(Integer dataModelId) {
        return indicatorConfigMapper.selectByDataModelId(dataModelId);
    }

    private StatisticPeriod calculateCurrentPeriod(IndicatorConfig indicatorConfig, IndicatorSearchRange searchRange) {
        IndicatorPeriodConfigEntity periodConfig = indicatorPeriodConfigMapper.selectByPeriodType(
            indicatorConfig.getStatisticStrategyInfo().getPeriod().name());

        StatisticPeriod period = IndicatorPeriodConverter.calculateStatPeriod(LocalDateTime.now(),
            periodConfig.getConfig());

        if (searchRange == IndicatorSearchRange.LAST) {
            period = IndicatorPeriodConverter.calculateStatPeriod(period.getStartTime(), periodConfig.getConfig());
        }
        return period;
    }

    private FieldGroups getFieldGroups(Integer dataModelId) {
        List<DataModelIndicatorField> allFields = getIndicatorFields(dataModelId);

        List<DataModelIndicatorField> metaFields = allFields.stream()
            .filter(e -> e.getIndicatorType() == IndicatorFieldType.META).toList();

        List<DataModelIndicatorField> applicationFields = allFields.stream()
            .filter(e -> e.getIndicatorType() == IndicatorFieldType.APPLICATION).toList();

        String startTimeField = findTimeField(metaFields, "start_time");
        String endTimeField = findTimeField(metaFields, "end_time");

        return new FieldGroups(metaFields, applicationFields, startTimeField, endTimeField);
    }

    private List<DataModelIndicatorField> getIndicatorFields(Integer dataModelId) {
        List<DataModelField> dataModelFields = dataModelFieldMapper.selectByDataModelId(dataModelId);
        List<DataModelIndicatorFieldEntity> fieldEntities = indicatorFieldMapper.listByDataModelId(dataModelId);

        Map<Integer, DataModelField> fieldMap = dataModelFields.stream()
            .collect(Collectors.toMap(DataModelField::getId, Function.identity()));

        return fieldEntities.stream().map(field -> {
            DataModelField dataModelField = fieldMap.get(field.getDataModelFieldId());
            if (dataModelField == null) {
                return null;
            }
            return new DataModelIndicatorField(dataModelField, field.getIndicatorType(), field.isEnable(),
                field.getOriginalZhName(), field.getConfig(), field.getExecuteOrder());
        }).filter(Objects::nonNull).toList();
    }

    private String findTimeField(List<DataModelIndicatorField> fields, String fieldNamePart) {
        return fields.stream().filter(e -> e.getEnName().contains(fieldNamePart)).findFirst()
            .map(DataModelIndicatorField::getEnName).orElse(null);
    }

    private IndicatorStatParams buildStatParams(IndicatorConfig indicatorConfig, FieldGroups fieldGroups,
        StatisticPeriod currentPeriod) {

        IndicatorStatParams params = new IndicatorStatParams();
        params.setDataModelId(indicatorConfig.getDataModelId());

        // 设置目标字段
        fieldGroups.metaFields().stream().filter(e -> ((IndicatorMetaFieldConfig) e.getConfig()).getIsCompute())
            .findFirst().ifPresent(params::setTargetField);

        // 设置统计维度
        params.setStatisticDims(
            indicatorConfig.getStatisticStrategyInfo().getStatisticDims().stream().map(AbstractField::getEnName)
                .toList());

        params.setStartTimeField(fieldGroups.startTimeField());

        // 计算时间差
        IndicatorPeriodConfigEntity periodConfig = indicatorPeriodConfigMapper.selectByPeriodType(
            indicatorConfig.getStatisticStrategyInfo().getPeriod().name());

        StatisticPeriod lastPeriod = IndicatorPeriodConverter.calculateStatPeriod(currentPeriod.getStartTime(),
            periodConfig.getConfig());
        params.setPeriodType(periodConfig.getPeriodType());
        switch (periodConfig.getPeriodType()) {
            case DAILY -> params.setDiffPeriodNum(
                ChronoUnit.DAYS.between(lastPeriod.getStartTime(), currentPeriod.getStartTime()));
            case WEEKLY -> params.setDiffPeriodNum(
                ChronoUnit.WEEKS.between(lastPeriod.getStartTime(), currentPeriod.getStartTime()));
            case MONTHLY, QUARTERLY, SEMIANNUALLY -> params.setDiffPeriodNum(
                ChronoUnit.MONTHS.between(lastPeriod.getStartTime(), currentPeriod.getStartTime()));
            case YEARLY -> params.setDiffPeriodNum(
                ChronoUnit.YEARS.between(lastPeriod.getStartTime(), currentPeriod.getStartTime()));
            default -> params.setDiffPeriodNum(0);
        }

        return params;
    }

    private Map<IndicatorSearchRange, List<Map<String, Object>>> getHistoricalData(DataConnection dataConnection,
        IndicatorDataSearchParams request,
        String tableName, FieldGroups fieldGroups, IndicatorStatParams statParams, StatisticPeriod currentPeriod) {

        Map<IndicatorSearchRange, List<Map<String, Object>>> data = new EnumMap<>(IndicatorSearchRange.class);
        IndicatorQueryBuilder queryBuilder = new IndicatorQueryBuilder(request, fieldGroups, dataConnection);
        // 获取上一个周期数据
        Integer connectionId = dataConnection.getId();
        StatisticPeriod lastPeriod = new StatisticPeriod(
            StatisticPeriod.minusPeriod(statParams.getPeriodType(), statParams.getDiffPeriodNum(),
                currentPeriod.getStartTime()),
            StatisticPeriod.minusPeriod(statParams.getPeriodType(), statParams.getDiffPeriodNum(),
                currentPeriod.getEndTime()));
        data.put(IndicatorSearchRange.LAST, getDataByPeriod(connectionId, tableName, lastPeriod, queryBuilder));

        // 获取去年同期数据
        StatisticPeriod lastYear = new StatisticPeriod(currentPeriod.getStartTime().minusYears(1),
            currentPeriod.getEndTime().minusYears(1));
        data.put(IndicatorSearchRange.LAST_YEAR,
            getDataByPeriod(connectionId, tableName, lastYear, queryBuilder));

        if (IndicatorPeriodType.DAILY.equals(statParams.getPeriodType())) {
            IndicatorPeriodConfigEntity periodConfig = indicatorPeriodConfigMapper.selectByPeriodType(
                IndicatorPeriodType.WEEKLY.name());

            StatisticPeriod week = IndicatorPeriodConverter.calculateStatPeriod(currentPeriod.getStartTime(),
                periodConfig.getConfig());
            week = new StatisticPeriod(week.getStartTime().plusWeeks(1), week.getEndTime().plusWeeks(1));
            // 获取week开始时间和currentPeriod开始时间的差值
            long weekDiff = ChronoUnit.DAYS.between(week.getStartTime(), currentPeriod.getStartTime());
            statParams.setNeighborDiffPeriodNum(weekDiff);
            data.put(IndicatorSearchRange.NEIGHBOR,
                getDataByPeriod(connectionId, tableName, week, queryBuilder));

            StatisticPeriod previousPeriod = new StatisticPeriod(currentPeriod.getStartTime().minusDays(3),
                currentPeriod.getEndTime());

            data.put(IndicatorSearchRange.PREVIOUS,
                getDataByPeriod(connectionId, tableName, previousPeriod, queryBuilder));
        }

        return data;
    }


    private void applyFieldCalculations(List<Map<String, Object>> currentData, FieldGroups fieldGroups,
        IndicatorStatParams statParams, Map<IndicatorSearchRange, List<Map<String, Object>>> historicalData) {

        for (DataModelIndicatorField field : fieldGroups.applicationFields()) {
            if (!field.isEnable()) {
                continue;
            }

            IndicatorApplicationFieldConfig config = (IndicatorApplicationFieldConfig) field.getConfig();

            statParams.setResultField(field);
            statParams.setFieldConfig(config);

            List<Map<String, Object>> lastData = selectHistoricalData(historicalData, config);
            // 创建currentData的深拷贝，而不是直接添加引用
            List<Map<String, Object>> currentDataCopy = currentData.stream().map(HashMap::new)
                .collect(Collectors.toList());
            // 合并数据用于计算
            lastData.addAll(currentDataCopy);

            statOperationFactory.doCalculate(currentData, lastData, statParams);
        }
    }

    private List<Map<String, Object>> selectHistoricalData(
        Map<IndicatorSearchRange, List<Map<String, Object>>> historicalData, IndicatorApplicationFieldConfig config) {
        List<Map<String, Object>> originalData;
        IndicatorStatOperation operation = config.getOperation();
        IndicatorSearchRange searchRange = config.getSearchRange();

        if (operation == IndicatorStatOperation.YEAR_ON_YEAR || searchRange == IndicatorSearchRange.LAST_YEAR) {
            originalData = historicalData.getOrDefault(IndicatorSearchRange.LAST_YEAR, new ArrayList<>());
        } else if (operation == IndicatorStatOperation.NEIGHBOR_TOTAL
            || operation == IndicatorStatOperation.COMPARISON_COUNT) {
            originalData = historicalData.getOrDefault(IndicatorSearchRange.NEIGHBOR, new ArrayList<>());
        } else if (operation == IndicatorStatOperation.TREND_CHANGE) {
            originalData = switch (searchRange) {
                case CURRENT -> historicalData.getOrDefault(IndicatorSearchRange.NEIGHBOR, new ArrayList<>());
                case PREVIOUS -> historicalData.getOrDefault(IndicatorSearchRange.PREVIOUS, new ArrayList<>());
                default -> new ArrayList<>();
            };
        } else if (searchRange == IndicatorSearchRange.PREVIOUS) {
            originalData = historicalData.getOrDefault(IndicatorSearchRange.PREVIOUS, new ArrayList<>());
        } else if (operation.isNeedLastData() || searchRange == IndicatorSearchRange.LAST) {
            originalData = historicalData.getOrDefault(IndicatorSearchRange.LAST, new ArrayList<>());
        } else {
            originalData = new ArrayList<>();
        }
        return originalData.stream().map(HashMap::new).collect(Collectors.toList());
    }

    private List<Map<String, Object>> sortData(List<Map<String, Object>> data, IndicatorSortField sortField,
        List<String> applicationFields) {

        if (sortField == null) {
            return data;
        }

        // 固定排序处理
        if (sortField.getOrder() == IndicatorSortOrder.FIXED || applicationFields.contains(sortField.getField())) {
            return applySorting(data, sortField);
        }
        return data;
    }

    private List<Map<String, Object>> applySorting(List<Map<String, Object>> data, IndicatorSortField sortField) {
        String fieldName = sortField.getField();

        switch (sortField.getOrder()) {
            case ASC:
                data.sort(createComparator(fieldName, false));
                break;
            case DESC:
                data.sort(createComparator(fieldName, true));
                break;
            case FIXED:
                if (sortField.getFixedOrder() != null && !sortField.getFixedOrder().isEmpty()) {
                    data.sort(createFixedOrderComparator(fieldName, sortField.getFixedOrder()));
                }
                break;
            default:
                break;
        }
        return data;
    }

    private Comparator<Map<String, Object>> createComparator(String fieldName, boolean descending) {
        return (m1, m2) -> {
            Object v1 = m1.get(fieldName);
            Object v2 = m2.get(fieldName);

            int comparison = compareValues(v1, v2);
            return descending ? -comparison : comparison;
        };
    }

    private Comparator<Map<String, Object>> createFixedOrderComparator(String fieldName, List<String> fixedOrder) {
        Map<String, Integer> orderMap = new HashMap<>();
        for (int i = 0; i < fixedOrder.size(); i++) {
            orderMap.put(fixedOrder.get(i), i);
        }

        return (m1, m2) -> {
            Object v1 = m1.get(fieldName);
            Object v2 = m2.get(fieldName);

            int index1 = orderMap.getOrDefault(String.valueOf(v1), Integer.MAX_VALUE);
            int index2 = orderMap.getOrDefault(String.valueOf(v2), Integer.MAX_VALUE);

            return Integer.compare(index1, index2);
        };
    }

    private int compareValues(Object v1, Object v2) {
        // 处理null值
        if (v1 == null && v2 == null) {
            return 0;
        }
        if (v1 == null) {
            return 1;
        }
        if (v2 == null) {
            return -1;
        }
        Integer result;
        // 1. 百分比字符串比较
        result = comparePercentages(v1, v2);
        if (Objects.nonNull(result)) {
            return result;
        }
        // 2. 相同类型对象比较
        result = compareMatchingTypes(v1, v2);
        if (Objects.nonNull(result)) {
            return result;
        }
        // 3. 数值类型比较
        result = compareNumbers(v1, v2);
        if (Objects.nonNull(result)) {
            return result;
        }
        // 4. 默认字符串比较
        return String.valueOf(v1).compareTo(String.valueOf(v2));
    }

    private Integer comparePercentages(Object v1, Object v2) {
        if (v1 instanceof String s1 && s1.trim().endsWith("%") && v2 instanceof String s2 && s2.trim().endsWith("%")) {
            try {
                double d1 = parsePercentage(s1.trim());
                double d2 = parsePercentage(s2.trim());
                return Double.compare(d1, d2);
            } catch (NumberFormatException e) {
                // 解析失败，返回null表示无法比较
            }
        }
        return null;
    }

    private Integer compareMatchingTypes(Object v1, Object v2) {
        try {
            if (v1 instanceof Comparable n1 && v1.getClass() == v2.getClass()) {
                return n1.compareTo(v2);
            }
        } catch (Exception e) {
            // 类型不匹配或比较失败
        }
        return null;
    }

    private Integer compareNumbers(Object v1, Object v2) {
        if (v1 instanceof Number n1 && v2 instanceof Number n2) {
            return Double.compare(n1.doubleValue(), n2.doubleValue());
        }
        return null;
    }

    private double parsePercentage(String percentStr) {
        return Double.parseDouble(percentStr.substring(0, percentStr.length() - 1).trim());
    }

    private List<Map<String, Object>> filterReturnFields(List<Map<String, Object>> data, List<String> returnFields) {
        if (returnFields == null || returnFields.isEmpty()) {
            return data;
        }

        return data.stream().map(row -> {
            Map<String, Object> filteredRow = new HashMap<>();
            returnFields.forEach(field -> {
                if (row.containsKey(field)) {
                    filteredRow.put(field, row.get(field));
                }
            });
            return filteredRow;
        }).toList();
    }

    private List<Map<String, Object>> getDataByPeriod(Integer connectionId, String tableName,
        StatisticPeriod period, IndicatorQueryBuilder queryBuilder) {
        try {
            String sql = queryBuilder.buildHistoricalQuerySql(tableName, period);
            return executeQuery(connectionId, sql);
        } catch (Exception e) {
            log.error("查询周期数据失败, 表名: {}, 周期: {} - {}", tableName,
                DateTimeUtils.formatStr(period.getStartTime()), DateTimeUtils.formatStr(period.getEndTime()), e);
            throw new BizException("查询周期数据失败", e);
        }
    }

    private List<Map<String, Object>> executeQuery(Integer connectionId, String sql) {
        CodeSearchParams codeSearchParams = new CodeSearchParams();
        codeSearchParams.setCode(sql);
        return searchFeign.indicatorQuery(connectionId, codeSearchParams);
    }
}


