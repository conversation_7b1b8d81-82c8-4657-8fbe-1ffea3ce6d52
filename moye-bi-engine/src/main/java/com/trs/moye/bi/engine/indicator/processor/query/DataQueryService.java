package com.trs.moye.bi.engine.indicator.processor.query;

import com.trs.moye.bi.engine.common.request.CodeSearchParams;
import com.trs.moye.bi.engine.feign.SearchFeign;
import com.trs.moye.bi.engine.indicator.context.IndicatorDataContext;
import com.trs.moye.bi.engine.indicator.context.QueryContext;
import com.trs.moye.bi.engine.indicator.exception.IndicatorQueryException;
import com.trs.moye.bi.engine.indicator.util.IndicatorQueryBuilder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 基础数据查询服务
 * <p>
 * 负责执行基础的数据查询操作，包括当前周期数据查询、SQL执行、数据上下文初始化等核心查询功能
 * </p>
 *
 * <AUTHOR>
 * @since 2025/07/28
 */
@Slf4j
@Service
public class DataQueryService {

    @Resource
    private SearchFeign searchFeign;

    /**
     * 执行当前周期数据查询
     * <p>
     * 根据查询上下文构建SQL查询语句，执行当前统计周期的数据查询操作 如果上下文中有缓存的分离条件，则使用分离后的SQL条件构建查询
     * </p>
     *
     * @param context 查询上下文，包含查询条件和配置信息
     * @return 当前周期的查询结果数据列表
     * @throws IndicatorQueryException 当数据查询失败时抛出
     */
    public List<Map<String, Object>> executeCurrentDataQuery(QueryContext context) {
        try {
            log.debug("执行当前周期数据查询, dataModelId: {}", context.getDataModelId());

            // 构建查询SQL
            IndicatorQueryBuilder queryBuilder = new IndicatorQueryBuilder(
                context.getRequest(), context.getFieldGroups(), context.getDataConnection());
            String sql = queryBuilder.buildConditionalQuerySql();

            // 执行查询
            List<Map<String, Object>> result = executeQuery(context.getConnectionId(), sql);

            log.debug("当前周期数据查询完成, 结果数量: {}", result.size());
            return result;

        } catch (Exception e) {
            log.error("当前周期数据查询失败, dataModelId: {}", context.getDataModelId(), e);
            throw IndicatorQueryException.dataQueryError("当前周期数据查询", e);
        }
    }

    /**
     * 执行SQL查询
     * <p>
     * 通过搜索服务执行指定的SQL查询语句，返回查询结果
     * </p>
     *
     * @param connectionId 数据库连接ID
     * @param sql          SQL查询语句
     * @return 查询结果数据列表
     * @throws IndicatorQueryException 当SQL查询执行失败时抛出
     */
    public List<Map<String, Object>> executeQuery(Integer connectionId, String sql) {
        try {
            CodeSearchParams codeSearchParams = new CodeSearchParams();
            codeSearchParams.setCode(sql);
            return searchFeign.indicatorQuery(connectionId, codeSearchParams);
        } catch (Exception e) {
            log.error("SQL查询执行失败, connectionId: {}, sql: {}", connectionId, sql, e);
            throw IndicatorQueryException.dataQueryError(sql, e);
        }
    }

    /**
     * 初始化数据上下文
     * <p>
     * 根据当前数据和查询上下文，初始化指标数据上下文，包括：
     * <ul>
     *   <li>设置基础数据和当前周期数据</li>
     *   <li>配置时间字段和维度字段</li>
     *   <li>初始化键生成器用于数据关联</li>
     *   <li>设置统计周期和周期类型</li>
     * </ul>
     * </p>
     *
     * @param currentData 当前周期的查询数据
     * @param context     查询上下文，包含字段配置和统计信息
     * @throws IndicatorQueryException 当数据上下文初始化失败时抛出
     */
    public void initializeDataContext(List<Map<String, Object>> currentData, QueryContext context) {
        try {
            log.debug("初始化数据上下文, dataModelId: {}", context.getDataModelId());

            // 获取维度字段和时间字段
            List<String> dimsFields = context.getStatisticDimensions();
            String timeField = context.getFieldGroups().startTimeField();

            // 创建维度键生成器：将多个维度字段值连接成唯一键
            Function<Map<String, Object>, String> dimKeyGenerator = row -> dimsFields.stream()
                .map(f -> String.valueOf(row.get(f)))
                .collect(Collectors.joining("_"));

            // 创建完整键生成器：维度键 + 时间字段值
            Function<Map<String, Object>, String> keyGenerator = row ->
                dimKeyGenerator.apply(row) + "_" + row.get(timeField).toString();

            // 初始化数据上下文
            List<Map<String, Object>> baseData = new ArrayList<>(currentData);
            IndicatorDataContext.setContext(new IndicatorDataContext.DataContext(
                new IndicatorDataContext.BaseData(baseData),
                currentData,
                timeField,
                dimsFields,
                context.getRequest().getStatisticPeriod(),
                context.getPeriodConfig() != null ? context.getPeriodConfig().getPeriodType() : null,
                keyGenerator,
                dimKeyGenerator,
                searchFeign,
                context.getConnectionId()
            ));

            log.debug("数据上下文初始化完成");

        } catch (Exception e) {
            log.error("初始化数据上下文失败, dataModelId: {}", context.getDataModelId(), e);
            throw IndicatorQueryException.dataProcessingError("初始化数据上下文", e);
        }
    }

    /**
     * 获取当前周期数据
     * <p>
     * 从数据上下文中获取当前统计周期的数据，如果上下文中没有数据则返回空列表
     * </p>
     *
     * @return 当前周期数据列表，如果没有数据则返回空列表
     */
    public List<Map<String, Object>> getCurrentPeriodData() {
        return IndicatorDataContext.getCurrentPeriodData().orElse(Collections.emptyList());
    }
}
