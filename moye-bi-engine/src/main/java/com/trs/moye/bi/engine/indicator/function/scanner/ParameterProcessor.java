package com.trs.moye.bi.engine.indicator.function.scanner;

import com.trs.moye.bi.engine.indicator.annotation.IndicatorParameter;
import com.trs.moye.bi.engine.indicator.context.ParameterContext;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 参数处理器
 * <p>
 * 负责处理方法调用参数的准备、验证和类型转换 职责包括：参数值提取、默认值处理、参数验证、类型转换
 * </p>
 *
 * <AUTHOR>
 * @since 2025/08/01
 */
@Slf4j
public class ParameterProcessor {

    private final ParameterTypeManager typeManager;

    public ParameterProcessor() {
        this.typeManager = new ParameterTypeManager();
    }

    /**
     * 准备方法调用参数
     * <p>
     * 智能参数准备策略：
     * <ol>
     *   <li>优先从参数上下文中获取参数值</li>
     *   <li>如果参数不存在且有默认值，使用默认值</li>
     *   <li>如果参数不存在且无默认值，根据参数类型提供合理的默认值</li>
     *   <li>支持类型转换和验证</li>
     * </ol>
     * </p>
     *
     * @param method  方法对象
     * @param context 参数上下文
     * @return 方法调用参数数组
     */
    public Object[] prepareMethodArguments(Method method, ParameterContext context) {
        Parameter[] methodParams = method.getParameters();
        Object[] args = new Object[methodParams.length];

        log.debug("准备方法 {} 的调用参数，需要 {} 个参数", method.getName(), methodParams.length);

        for (int i = 0; i < methodParams.length; i++) {
            args[i] = prepareMethodArgument(methodParams[i], context, i);
        }

        return args;
    }

    /**
     * 准备单个方法参数
     * <p>
     * 处理单个参数的获取、默认值设置和验证逻辑
     * </p>
     *
     * @param param   方法参数
     * @param context 参数上下文
     * @param index   参数索引
     * @return 参数值
     */
    private Object prepareMethodArgument(Parameter param, ParameterContext context, int index) {
        IndicatorParameter paramAnnotation = param.getAnnotation(IndicatorParameter.class);

        if (paramAnnotation == null) {
            log.warn("方法参数 {} 缺少 @IndicatorParameter 注解", param.getName());
            return null;
        }

        String paramName = paramAnnotation.name();
        Class<?> paramType = param.getType();

        // 提取参数值
        Object value = extractParameterValue(context, paramName, paramType);

        // 处理默认值
        if (value == null) {
            value = handleDefaultValue(paramAnnotation, paramType, paramName);
        }

        // 验证必需参数
        validateRequiredParameter(paramAnnotation, paramName, value);

        log.debug("参数 {}: {} = {} ({})", index, paramName, value,
            value != null ? value.getClass().getSimpleName() : "null");

        return value;
    }

    /**
     * 提取参数值
     * <p>
     * 从参数上下文中获取指定类型的参数值
     * </p>
     *
     * @param context   参数上下文
     * @param paramName 参数名称
     * @param paramType 参数类型
     * @return 参数值
     */
    private Object extractParameterValue(ParameterContext context, String paramName, Class<?> paramType) {
        try {
            return typeManager.getParameterValue(context, paramName, paramType);
        } catch (Exception e) {
            log.warn("提取参数值失败: {} ({})", paramName, paramType.getSimpleName(), e);
            return null;
        }
    }

    /**
     * 处理默认值
     * <p>
     * 当参数值为空时，根据注解配置和参数类型提供合适的默认值
     * </p>
     *
     * @param paramAnnotation 参数注解
     * @param paramType       参数类型
     * @param paramName       参数名称
     * @return 默认值
     */
    private Object handleDefaultValue(IndicatorParameter paramAnnotation, Class<?> paramType, String paramName) {
        if (!paramAnnotation.required() && StringUtils.isNotBlank(paramAnnotation.defaultValue())) {
            // 使用注解中定义的默认值，通过 ParameterTypeManager 统一转换
            Object defaultValue = typeManager.convertStringToType(paramType, paramAnnotation.defaultValue());
            log.debug("参数 {} 使用注解默认值: {}", paramName, defaultValue);
            return defaultValue;
        } else if (!paramAnnotation.required()) {
            // 为可选参数提供类型相关的默认值
            Object typeDefaultValue = typeManager.getTypeDefaultValue(paramType);
            log.debug("参数 {} 使用类型默认值: {}", paramName, typeDefaultValue);
            return typeDefaultValue;
        }
        return null;
    }

    /**
     * 验证必需参数
     * <p>
     * 检查必需参数是否有值，如果没有则记录警告
     * </p>
     *
     * @param paramAnnotation 参数注解
     * @param paramName       参数名称
     * @param value           参数值
     */
    private void validateRequiredParameter(IndicatorParameter paramAnnotation, String paramName, Object value) {
        if (paramAnnotation.required() && value == null) {
            log.warn("必需参数 {} 的值为空，可能导致执行错误", paramName);
        }
    }
}
