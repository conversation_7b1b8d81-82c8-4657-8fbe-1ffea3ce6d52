package com.trs.moye.bi.engine.indicator.function.scanner;

import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.bi.engine.indicator.context.ParameterContext;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

/**
 * 参数类型管理器
 * <p>
 * 使用 Map 映射管理不同参数类型的处理逻辑，提供统一的参数获取、默认值处理和类型转换接口 支持的参数类型包括：String、Integer、Boolean、List、Map、Enum 和其他 Object 类型
 * </p>
 *
 * <AUTHOR>
 * @since 2025/08/01
 */
@Slf4j
public class ParameterTypeManager {

    /**
     * 参数值获取函数接口
     * <p>
     * 定义从参数上下文中获取特定类型参数值的函数接口
     * </p>
     */
    @FunctionalInterface
    private interface ParameterValueExtractor {

        /**
         * 从参数上下文中提取参数值
         *
         * @param context   参数上下文
         * @param paramName 参数名称
         * @return 参数值
         */
        Object extract(ParameterContext context, String paramName);
    }

    /**
     * 参数类型配置
     * <p>
     * 封装参数类型的值提取器和默认值
     * </p>
     *
     * @param extractor    参数值提取器
     * @param defaultValue 默认值
     */
    private record ParameterTypeConfig(ParameterValueExtractor extractor, Object defaultValue) {

        public Object extractValue(ParameterContext context, String paramName) {
            return extractor.extract(context, paramName);
        }
    }

    private final Map<Class<?>, ParameterTypeConfig> typeConfigMap;
    private final Map<Class<?>, ParameterTypeConfig> primitiveTypeMap;

    public ParameterTypeManager() {
        // 初始化基本类型配置映射
        this.typeConfigMap = initializeTypeConfigMap();
        this.primitiveTypeMap = initializePrimitiveTypeMap();
    }

    /**
     * 初始化参数类型配置映射
     *
     * @return 类型配置映射
     */
    private Map<Class<?>, ParameterTypeConfig> initializeTypeConfigMap() {
        Map<Class<?>, ParameterTypeConfig> configMap = new HashMap<>();

        // 字符串类型
        configMap.put(String.class, new ParameterTypeConfig(
            ParameterContext::getString, ""));

        // 整数类型
        configMap.put(Integer.class, new ParameterTypeConfig(
            ParameterContext::getInteger, 0));

        // 布尔类型
        configMap.put(Boolean.class, new ParameterTypeConfig(
            ParameterContext::getBoolean, false));

        // 列表类型
        configMap.put(List.class, new ParameterTypeConfig(
            ParameterContext::getStringList, new ArrayList<>()));

        // 映射类型
        configMap.put(Map.class, new ParameterTypeConfig(
            (context, paramName) -> JsonUtils.toMap(context.getString(paramName), String.class, Object.class),
            new HashMap<>()));

        return configMap;
    }

    /**
     * 初始化基本数据类型配置映射
     *
     * @return 基本数据类型配置映射
     */
    private Map<Class<?>, ParameterTypeConfig> initializePrimitiveTypeMap() {
        Map<Class<?>, ParameterTypeConfig> primitiveMap = new HashMap<>();

        // int 基本类型
        primitiveMap.put(int.class, new ParameterTypeConfig(
            ParameterContext::getInteger, 0));

        // boolean 基本类型
        primitiveMap.put(boolean.class, new ParameterTypeConfig(
            ParameterContext::getBoolean, false));

        return primitiveMap;
    }

    /**
     * 从参数上下文中获取指定类型的参数值
     *
     * @param context   参数上下文
     * @param paramName 参数名称
     * @param paramType 参数类型
     * @return 参数值
     */
    public Object getParameterValue(ParameterContext context, String paramName, Class<?> paramType) {
        // 处理枚举类型
        if (paramType.isEnum()) {
            return context.getEnum(paramName, (Class) paramType);
        }

        // 查找类型配置
        ParameterTypeConfig config = getTypeConfig(paramType);
        if (config != null) {
            return config.extractValue(context, paramName);
        }

        // 默认使用 getObject 方法
        return context.getObject(paramName);
    }

    /**
     * 获取指定类型的默认值
     *
     * @param paramType 参数类型
     * @return 类型默认值
     */
    public Object getTypeDefaultValue(Class<?> paramType) {
        // 处理枚举类型
        if (paramType.isEnum()) {
            return null;
        }

        // 查找类型配置
        ParameterTypeConfig config = getTypeConfig(paramType);
        if (config != null) {
            return config.defaultValue();
        }

        // 默认返回 null
        return null;
    }

    /**
     * 将字符串值转换为指定类型
     * <p>
     * 统一的类型转换方法，支持基本类型和枚举类型的转换 用于处理注解中定义的默认值字符串
     * </p>
     *
     * @param paramType 目标参数类型
     * @param value     字符串值
     * @return 转换后的值
     */
    public Object convertStringToType(Class<?> paramType, String value) {
        try {
            if (paramType == String.class) {
                return value;
            } else if (paramType == Integer.class || paramType == int.class) {
                return Integer.valueOf(value);
            } else if (paramType == Boolean.class || paramType == boolean.class) {
                return Boolean.valueOf(value);
            } else if (paramType.isEnum()) {
                Class<? extends Enum<?>> enumClass = (Class<? extends Enum<?>>) paramType;
                return Enum.valueOf((Class) enumClass, value);
            } else {
                return value;
            }
        } catch (Exception e) {
            log.warn("字符串转换为类型失败: {} -> {}", value, paramType.getSimpleName(), e);
            return value;
        }
    }

    /**
     * 获取参数类型配置
     *
     * @param paramType 参数类型
     * @return 类型配置，如果不存在返回 null
     */
    private ParameterTypeConfig getTypeConfig(Class<?> paramType) {
        // 优先查找精确类型匹配
        ParameterTypeConfig config = typeConfigMap.get(paramType);
        if (config != null) {
            return config;
        }

        // 查找基本数据类型匹配
        return primitiveTypeMap.get(paramType);
    }
}
