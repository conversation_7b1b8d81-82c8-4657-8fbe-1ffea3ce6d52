package com.trs.moye.bi.engine.indicator.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 计算函数参数定义
 * <p>
 * 定义计算函数的单个参数信息，包括参数名称、类型、是否必需、默认值等 用于支持命名参数配置和参数验证
 * </p>
 *
 * <AUTHOR>
 * @since 2025/07/29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CalculationParameter {

    /**
     * 参数名称
     * <p>
     * 用于命名参数配置中的键名，应该具有明确的语义 例如：targetField, operator, comparisonValue
     * </p>
     */
    private String name;

    /**
     * 参数类型
     * <p>
     * 支持的类型包括：String.class, Integer.class, Boolean.class, EvaluateOperator.class 等
     * </p>
     */
    private Class<?> type;

    /**
     * 是否必需参数
     * <p>
     * true: 必需参数，缺失时会抛出异常 false: 可选参数，缺失时使用默认值
     * </p>
     */
    private boolean required;

    /**
     * 默认值
     * <p>
     * 当参数为可选且未提供时使用的默认值 必须与参数类型兼容
     * </p>
     */
    private Object defaultValue;

    /**
     * 参数描述
     * <p>
     * 用于界面显示和文档生成，描述参数的用途和取值范围
     * </p>
     */
    private String description;

    /**
     * 在旧版（2025.7.30前）列表参数中的位置索引
     * <p>
     * 用于向后兼容，指定该参数在原始 List&lt;String&gt; 中的位置 从0开始计数，-1表示不在列表中
     * </p>
     */
    private int legacyIndex;

    /**
     * 创建必需参数
     *
     * @param name        参数名称
     * @param type        参数类型
     * @param description 参数描述
     * @param legacyIndex 在传统列表中的索引位置
     * @return 参数定义对象
     */
    public static CalculationParameter required(String name, Class<?> type, String description, int legacyIndex) {
        return CalculationParameter.builder()
            .name(name)
            .type(type)
            .required(true)
            .description(description)
            .legacyIndex(legacyIndex)
            .build();
    }

    /**
     * 创建可选参数
     *
     * @param name         参数名称
     * @param type         参数类型
     * @param defaultValue 默认值
     * @param description  参数描述
     * @param legacyIndex  在传统列表中的索引位置
     * @return 参数定义对象
     */
    public static CalculationParameter optional(String name, Class<?> type, Object defaultValue,
        String description, int legacyIndex) {
        return CalculationParameter.builder()
            .name(name)
            .type(type)
            .required(false)
            .defaultValue(defaultValue)
            .description(description)
            .legacyIndex(legacyIndex)
            .build();
    }

    /**
     * 创建可选参数（无默认值）
     *
     * @param name        参数名称
     * @param type        参数类型
     * @param description 参数描述
     * @param legacyIndex 在传统列表中的索引位置
     * @return 参数定义对象
     */
    public static CalculationParameter optional(String name, Class<?> type, String description, int legacyIndex) {
        return CalculationParameter.builder()
            .name(name)
            .type(type)
            .required(false)
            .description(description)
            .legacyIndex(legacyIndex)
            .build();
    }

    /**
     * 检查参数类型是否匹配
     *
     * @param value 要检查的值
     * @return 如果类型匹配返回true，否则返回false
     */
    public boolean isTypeMatch(Object value) {
        if (value == null) {
            return !required;
        }

        // 字符串可以转换为大多数类型
        if (value instanceof String) {
            return true;
        }

        return type.isAssignableFrom(value.getClass());
    }

    /**
     * 获取参数的显示名称
     * <p>
     * 用于界面显示，如果有描述则返回描述，否则返回参数名称
     * </p>
     *
     * @return 显示名称
     */
    public String getDisplayName() {
        return description != null && !description.trim().isEmpty() ? description : name;
    }
}
