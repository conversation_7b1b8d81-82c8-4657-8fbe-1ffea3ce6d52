package com.trs.moye.bi.engine.indicator.config;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import javax.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 指标查询线程池配置.
 * <p>
 * 为指标查询提供专用的线程池，用于并行处理历史数据查询等耗时操作
 *
 * <AUTHOR>
 * @since 2025/07/23
 */
@Slf4j
@Configuration
public class IndicatorThreadPoolConfig {

    /** 核心线程数. */
    @Value("${indicator.thread-pool.core-size:4}")
    private int corePoolSize;

    /** 最大线程数. */
    @Value("${indicator.thread-pool.max-size:8}")
    private int maximumPoolSize;

    /** 线程保活时间. */
    @Value("${indicator.thread-pool.keep-alive-time:60}")
    private long keepAliveTime;

    /** 队列容量. */
    @Value("${indicator.thread-pool.queue-capacity:100}")
    private int queueCapacity;

    /** 指标查询执行器. */
    private ExecutorService indicatorQueryExecutor;

    /**
     * 创建指标查询专用线程池.
     *
     * @return 线程池执行器
     */
    @Bean("indicatorQueryExecutor")
    public ExecutorService indicatorQueryExecutor() {
        final ThreadFactory threadFactory = new IndicatorThreadFactory();

        this.indicatorQueryExecutor = new ThreadPoolExecutor(
            corePoolSize,
            maximumPoolSize,
            keepAliveTime,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(queueCapacity),
            threadFactory,
            new ThreadPoolExecutor.CallerRunsPolicy() // 当队列满时，由调用线程执行
        );

        log.info("指标查询线程池初始化完成 - 核心线程数: {}, 最大线程数: {}, 队列容量: {}",
            corePoolSize, maximumPoolSize, queueCapacity);

        return this.indicatorQueryExecutor;
    }

    /**
     * 应用关闭时优雅关闭线程池.
     */
    @PreDestroy
    public void shutdown() {
        if (indicatorQueryExecutor != null && !indicatorQueryExecutor.isShutdown()) {
            log.info("开始关闭指标查询线程池");

            indicatorQueryExecutor.shutdown();
            try {
                // 等待正在执行的任务完成
                final int shutdownTimeout = 30;
                if (!indicatorQueryExecutor.awaitTermination(shutdownTimeout, TimeUnit.SECONDS)) {
                    log.warn("线程池未能在30秒内正常关闭，强制关闭");
                    indicatorQueryExecutor.shutdownNow();

                    // 再次等待
                    final int forceShutdownTimeout = 10;
                    if (!indicatorQueryExecutor.awaitTermination(forceShutdownTimeout,
                            TimeUnit.SECONDS)) {
                        log.error("线程池强制关闭失败");
                    }
                }
                log.info("指标查询线程池关闭完成");
            } catch (InterruptedException e) {
                log.error("等待线程池关闭时被中断", e);
                indicatorQueryExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 指标查询专用线程工厂.
     */
    private static class IndicatorThreadFactory implements ThreadFactory {
        /** 线程编号. */
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        /** 线程名前缀. */
        private final String namePrefix = "indicator-query-";

        @Override
        public Thread newThread(final Runnable r) {
            final Thread thread = new Thread(r, namePrefix + threadNumber.getAndIncrement());
            thread.setDaemon(false);
            thread.setPriority(Thread.NORM_PRIORITY);

            // 设置未捕获异常处理器
            thread.setUncaughtExceptionHandler((t, e) ->
                log.error("指标查询线程 {} 发生未捕获异常", t.getName(), e));

            return thread;
        }
    }
}
