package com.trs.moye.bi.engine.indicator.v1.indicator;

import com.trs.moye.base.data.indicator.entity.IndicatorApplicationFieldConfig;
import com.trs.moye.base.data.indicator.entity.StatisticPeriod;
import com.trs.moye.base.data.indicator.enums.IndicatorStatOperation;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.springframework.stereotype.Component;

/**
 * 总数操作 基础代码实现
 */
@Component
public class NeighborTotalOperation extends AbstractStatOperation {

    @Override
    public void calculate(List<Map<String, Object>> currentData, List<Map<String, Object>> historyData,
        IndicatorStatParams params) {
        IndicatorApplicationFieldConfig fieldConfig = params.getFieldConfig();
        Integer periodIndex = fieldConfig.getPeriodIndex();
        String targetField = getTargetField(params);
        String resultField = getResultField(params);
        String startTime = params.getStartTimeField();
        List<String> dims = getStatisticDims(params);
        HistoryDataGroup grouped = groupHistoryData(historyData, startTime, dims);
        for (Map<String, Object> data : currentData) {
            LocalDateTime curTime = parseLocalDateTime(data.get(startTime));
            if (Objects.isNull(curTime)) {
                continue;
            }
            // 先减去 getDiffPeriodNum 的数量，再加上 periodIndex 的数量
            LocalDateTime neighborTime = StatisticPeriod.plusPeriod(
                params.getPeriodType(),
                periodIndex.longValue() - 1,
                StatisticPeriod.minusPeriod(
                    params.getPeriodType(),
                    params.getNeighborDiffPeriodNum(),
                    curTime
                )
            );
            Map<String, Object> hit = findMatchingHistoryItem(data, grouped, dims,
                neighborTime);
            if (Objects.nonNull(hit)) {
                data.put(resultField, hit.get(targetField));
            }
        }
    }

    @Override
    public boolean isSupport(IndicatorStatOperation operation) {
        return IndicatorStatOperation.NEIGHBOR_TOTAL.equals(operation);
    }

}
