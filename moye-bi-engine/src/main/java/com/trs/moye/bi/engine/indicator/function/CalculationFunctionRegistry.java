package com.trs.moye.bi.engine.indicator.function;

import com.trs.moye.bi.engine.indicator.entity.CalculationFunctionDefinition;
import com.trs.moye.bi.engine.indicator.function.scanner.IndicatorFunctionScanner;
import com.trs.moye.bi.engine.indicator.function.calculation.ArithmeticOperations;
import com.trs.moye.bi.engine.indicator.function.calculation.ComparativeAnalysis;
import com.trs.moye.bi.engine.indicator.function.calculation.ConditionalCalculations;
import com.trs.moye.bi.engine.indicator.function.calculation.DataAggregation;
import com.trs.moye.bi.engine.indicator.function.calculation.ExternalDataFetch;
import com.trs.moye.bi.engine.indicator.function.calculation.SequenceGeneration;
import com.trs.moye.bi.engine.indicator.function.calculation.TrendAnalysis;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

/**
 * 计算函数注册表
 * <p>
 * 使用注解扫描方式自动发现和注册计算函数
 * </p>
 *
 * <AUTHOR>
 * @since 2025/07/29
 */
@Slf4j
public class CalculationFunctionRegistry {

    private CalculationFunctionRegistry() {
    }

    /**
     * 函数定义映射表
     * <p>
     * 键为函数名称，值为函数定义
     * </p>
     */
    private static final Map<String, CalculationFunctionDefinition> FUNCTION_DEFINITIONS = new HashMap<>();

    // 静态初始化所有函数定义
    static {
        initializeFunctionDefinitions();
    }

    /**
     * 初始化所有函数定义
     * <p>
     * 使用注解扫描方式自动发现和注册计算函数 替代原有的硬编码注册方式
     * </p>
     */
    private static void initializeFunctionDefinitions() {
        log.info("开始使用注解扫描初始化计算函数定义");

        // 定义需要扫描的计算函数类
        Class<?>[] calculationClasses = {
            ArithmeticOperations.class,
            ComparativeAnalysis.class,
            ConditionalCalculations.class,
            DataAggregation.class,
            ExternalDataFetch.class,
            SequenceGeneration.class,
            TrendAnalysis.class
        };

        // 扫描每个类并注册函数定义
        for (Class<?> clazz : calculationClasses) {
            try {
                List<CalculationFunctionDefinition> definitions = IndicatorFunctionScanner.scanClass(clazz);
                for (CalculationFunctionDefinition definition : definitions) {
                    FUNCTION_DEFINITIONS.put(definition.getFunctionName(), definition);
                    log.debug("注册函数: {} ({})", definition.getFunctionName(), definition.getDescription());
                }
                log.info("扫描类 {} 完成，注册 {} 个函数", clazz.getSimpleName(), definitions.size());
            } catch (Exception e) {
                log.error("扫描类 {} 失败", clazz.getSimpleName(), e);
            }
        }

        log.info("计算函数定义初始化完成，共注册 {} 个函数", FUNCTION_DEFINITIONS.size());
    }

    /**
     * 根据函数名称获取函数定义
     *
     * @param functionName 函数名称
     * @return 函数定义，如果不存在返回空的Optional
     */
    public static Optional<CalculationFunctionDefinition> getDefinition(String functionName) {
        return Optional.ofNullable(FUNCTION_DEFINITIONS.get(functionName));
    }

    /**
     * 获取所有函数名称列表
     * <p>
     * 返回所有已注册函数的名称列表，该方法是线程安全的。 主要用于单元测试中的函数存在性验证和初始化检查。
     * </p>
     *
     * @return 所有已注册函数的名称列表，按字母顺序排序
     */
    public static List<String> getAllFunctions() {
        return FUNCTION_DEFINITIONS.keySet().stream()
            .sorted()
            .toList();
    }

    /**
     * 获取所有函数定义
     *
     * @return 所有函数定义的映射表
     */
    public static Map<String, CalculationFunctionDefinition> getAllDefinitions() {
        return new HashMap<>(FUNCTION_DEFINITIONS);
    }

    /**
     * 检查函数是否存在
     *
     * @param functionName 函数名称
     * @return 如果函数存在返回true，否则返回false
     */
    public static boolean hasFunction(String functionName) {
        return FUNCTION_DEFINITIONS.containsKey(functionName);
    }

    /**
     * 根据分类获取函数定义列表
     *
     * @param category 函数分类
     * @return 指定分类的函数定义列表
     */
    public static List<CalculationFunctionDefinition> getDefinitionsByCategory(String category) {
        return FUNCTION_DEFINITIONS.values().stream()
            .filter(def -> category.equals(def.getCategory()))
            .toList();
    }

    /**
     * 获取所有函数分类
     *
     * @return 所有函数分类的列表
     */
    public static List<String> getAllCategories() {
        return FUNCTION_DEFINITIONS.values().stream()
            .map(CalculationFunctionDefinition::getCategory)
            .distinct()
            .sorted()
            .toList();
    }

    /**
     * 获取函数统计信息
     *
     * @return 包含统计信息的Map
     */
    public static Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalFunctions", FUNCTION_DEFINITIONS.size());

        Map<String, Long> categoryStats = FUNCTION_DEFINITIONS.values().stream()
            .collect(java.util.stream.Collectors.groupingBy(
                CalculationFunctionDefinition::getCategory,
                java.util.stream.Collectors.counting()
            ));
        stats.put("categoryStats", categoryStats);

        return stats;
    }


    /**
     * 重新加载函数定义
     */
    public static void reload() {
        FUNCTION_DEFINITIONS.clear();
        initializeFunctionDefinitions();
        log.info("函数定义重新加载完成");
    }

    /**
     * 获取注册表的初始化状态
     * <p>
     * 用于测试环境中检查注册表是否已正确初始化。 如果注册表为空，可能需要调用 reload() 方法重新初始化。
     * </p>
     *
     * @return 如果注册表已初始化且包含函数定义返回true，否则返回false
     */
    public static boolean isInitialized() {
        return !FUNCTION_DEFINITIONS.isEmpty();
    }

    /**
     * 确保注册表已初始化
     * <p>
     * 如果注册表为空，则重新初始化。这个方法主要用于测试环境， 确保在执行测试前注册表处于正确的状态。
     * </p>
     */
    public static void ensureInitialized() {
        if (!isInitialized()) {
            log.warn("函数注册表未初始化，正在重新初始化...");
            reload();
        }
    }
}
