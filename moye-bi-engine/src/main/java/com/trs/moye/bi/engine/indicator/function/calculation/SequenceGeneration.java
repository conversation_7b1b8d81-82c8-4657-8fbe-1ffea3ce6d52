package com.trs.moye.bi.engine.indicator.function.calculation;

import com.trs.moye.bi.engine.indicator.annotation.DataGenerationType;
import com.trs.moye.bi.engine.indicator.annotation.IndicatorCategory;
import com.trs.moye.bi.engine.indicator.annotation.IndicatorFunction;
import com.trs.moye.bi.engine.indicator.context.IndicatorDataContext;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

/**
 * 序号生成计算类
 * <p>
 * 负责为数据生成序号字段，实际的序号生成逻辑在数据处理的最后阶段执行，此类主要用于标识序号生成需求
 * </p>
 *
 * <AUTHOR>
 * @since 2025/07/28
 */
@Slf4j
@IndicatorCategory(enAbbr = "utility", name = "工具函数", description = "提供辅助工具功能，包括序号生成等")
public final class SequenceGeneration {

    private SequenceGeneration() {
    }

    /**
     * 生成序号标识
     * <p>
     * 此方法主要用于在计算函数中标识需要生成序号的字段 实际的序号生成逻辑在 DataProcessingService.generateSequenceNumbers() 中执行 这样可以确保序号基于最终排序后的数据顺序生成
     * </p>
     *
     * @return 空的Map，表示此阶段不生成实际数据，仅作为标识
     */
    @IndicatorFunction(
        enName = "generateSequence",
        zhName = "生成序号",
        description = "为数据行生成序号，实际序号在数据处理最后阶段基于排序结果生成",
        dataGenerationType = DataGenerationType.DATA_GENERATOR
    )
    public static Map<String, Object> generateSequence() {
        return IndicatorDataContext.getCalculationContext().map(context -> {
            log.debug("标识序号生成需求，实际序号将在数据处理最后阶段生成");
            return Collections.<String, Object>emptyMap();

        }).orElse(Collections.emptyMap());
    }

    /**
     * 为数据列表生成序号字段
     * <p>
     * 当存在多个需要生成序号的字段时，使用此方法可以一次性处理 所有序号字段都基于相同的数据顺序生成
     * </p>
     *
     * @param data               待添加序号的数据列表
     * @param sequenceFieldNames 序号字段名称列表
     * @return 添加了所有序号字段的数据列表
     */
    public static List<Map<String, Object>> addMultipleSequencesToData(List<Map<String, Object>> data,
        List<String> sequenceFieldNames) {
        if (data == null || data.isEmpty() || sequenceFieldNames == null || sequenceFieldNames.isEmpty()) {
            return data;
        }

        try {
            log.debug("开始为数据生成序号字段，字段数量: {}, 数据量: {}",
                sequenceFieldNames.size(), data.size());

            // 为每行数据添加所有序号字段
            for (int i = 0; i < data.size(); i++) {
                Map<String, Object> row = data.get(i);
                int sequenceNumber = i + 1; // 序号从1开始

                // 为当前行添加所有序号字段
                for (String fieldName : sequenceFieldNames) {
                    if (fieldName != null && !fieldName.trim().isEmpty()) {
                        row.put(fieldName, sequenceNumber);
                    }
                }
            }

            log.debug("序号字段生成完成，字段数量: {}, 数据量: {}",
                sequenceFieldNames.size(), data.size());
            return data;

        } catch (Exception e) {
            log.error("序号生成失败，字段数量: {}", sequenceFieldNames.size(), e);
            // 发生异常时返回原始数据，不影响主流程
            return data;
        }
    }
}
