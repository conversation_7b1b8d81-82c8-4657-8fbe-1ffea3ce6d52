package com.trs.moye.bi.engine.indicator.function.scanner;

import com.trs.moye.bi.engine.indicator.annotation.IndicatorParameter;
import com.trs.moye.bi.engine.indicator.entity.CalculationParameter;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 参数解析器
 * <p>
 * 负责解析方法参数信息，构建 CalculationParameter 对象 职责包括：参数信息提取、参数必需性分析、默认值处理、参数定义构建
 * </p>
 *
 * <AUTHOR>
 * @since 2025/08/01
 */
@Slf4j
public class ParameterParser {

    private final ParameterTypeManager typeManager;

    public ParameterParser() {
        this.typeManager = new ParameterTypeManager();
    }

    /**
     * 解析合并后的参数定义
     * <p>
     * 智能分析所有重载方法，确定参数的必需性和默认值：
     * <ol>
     *   <li>必需参数：在所有重载方法中都存在的参数（通常是前几个参数）</li>
     *   <li>可选参数：只在部分重载方法中存在的参数</li>
     *   <li>默认值：从注解中获取可选参数的默认值</li>
     *   <li>参数顺序：按照参数最多的方法中的顺序排列</li>
     * </ol>
     * </p>
     *
     * @param overloadedMethods 重载方法列表
     * @return 合并后的参数定义列表
     */
    public List<CalculationParameter> parseMergedParameters(List<Method> overloadedMethods) {
        log.debug("开始解析 {} 个重载方法的参数定义", overloadedMethods.size());

        // 找到参数最少的方法，其参数都是必需的
        Method minParamMethod = findMethodWithMinParameters(overloadedMethods);
        // 找到参数最多的方法，包含所有可能的参数
        Method maxParamMethod = findMethodWithMaxParameters(overloadedMethods);

        int minParamCount = minParamMethod.getParameterCount();
        int maxParamCount = maxParamMethod.getParameterCount();
        Parameter[] maxParams = maxParamMethod.getParameters();

        log.debug("参数分析: 最少参数方法有 {} 个参数，最多参数方法有 {} 个参数",
            minParamCount, maxParamCount);

        List<CalculationParameter> parameters = new ArrayList<>();

        // 分析每个参数位置
        for (int i = 0; i < maxParams.length; i++) {
            Parameter param = maxParams[i];
            CalculationParameter calculationParam = buildParameterDefinition(param, i, minParamCount);
            if (calculationParam != null) {
                parameters.add(calculationParam);
            }
        }

        log.debug("参数解析完成，共 {} 个参数，其中 {} 个必需，{} 个可选",
            parameters.size(), minParamCount, parameters.size() - minParamCount);

        return parameters;
    }

    /**
     * 找到参数数量最少的方法
     *
     * @param methods 方法列表
     * @return 参数最少的方法
     */
    private Method findMethodWithMinParameters(List<Method> methods) {
        return methods.stream()
            .min((m1, m2) -> Integer.compare(m1.getParameterCount(), m2.getParameterCount()))
            .orElseThrow(() -> new IllegalArgumentException("重载方法列表为空"));
    }

    /**
     * 找到参数数量最多的方法
     *
     * @param methods 方法列表
     * @return 参数最多的方法
     */
    private Method findMethodWithMaxParameters(List<Method> methods) {
        return methods.stream()
            .max((m1, m2) -> Integer.compare(m1.getParameterCount(), m2.getParameterCount()))
            .orElseThrow(() -> new IllegalArgumentException("重载方法列表为空"));
    }

    /**
     * 构建参数定义
     *
     * @param param         参数对象
     * @param paramIndex    参数索引
     * @param minParamCount 最少参数数量
     * @return 参数定义对象
     */
    private CalculationParameter buildParameterDefinition(Parameter param, int paramIndex, int minParamCount) {
        IndicatorParameter paramAnnotation = param.getAnnotation(IndicatorParameter.class);

        if (paramAnnotation == null) {
            log.warn("参数 {} 缺少 @IndicatorParameter 注解", param.getName());
            return null;
        }

        // 优先使用注解中的 required 属性，如果注解明确指定了 required=false，则以注解为准
        // 否则，基于参数位置判断（前 minParamCount 个参数是必需的）
        boolean required;
        if (!paramAnnotation.required()) {
            // 注解明确指定为可选参数
            required = false;
        } else {
            // 注解未明确指定或指定为必需，基于参数位置判断
            required = paramIndex < minParamCount;
        }

        // 对于可选参数，检查是否在注解中定义了默认值
        Object defaultValue = null;
        if (!required && StringUtils.isNotBlank(paramAnnotation.defaultValue())) {
            // 使用 ParameterTypeManager 统一处理类型转换
            defaultValue = typeManager.convertStringToType(param.getType(), paramAnnotation.defaultValue());
        }

        CalculationParameter calculationParam = CalculationParameter.builder()
            .name(paramAnnotation.name())
            .type(param.getType())
            .required(required)
            .defaultValue(defaultValue)
            .description(paramAnnotation.description())
            .legacyIndex(paramIndex) // 自动设置为参数位置
            .build();

        log.debug("参数 {}: {} ({}), 必需: {} (注解required={}, 位置判断={}), 默认值: {}",
            paramIndex, paramAnnotation.name(), param.getType().getSimpleName(),
            required, paramAnnotation.required(), paramIndex < minParamCount, defaultValue);

        return calculationParam;
    }
}
