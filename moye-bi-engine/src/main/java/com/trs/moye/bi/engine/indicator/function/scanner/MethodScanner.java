package com.trs.moye.bi.engine.indicator.function.scanner;

import com.trs.moye.bi.engine.indicator.annotation.IndicatorFunction;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

/**
 * 方法扫描器
 * <p>
 * 负责扫描类中的方法，识别带有 @IndicatorFunction 注解的方法，并按函数名称分组 职责包括：方法识别、注解检查、方法分组
 * </p>
 *
 * <AUTHOR>
 * @since 2025/08/01
 */
@Slf4j
public class MethodScanner {

    /**
     * 扫描类中带有 @IndicatorFunction 注解的方法
     * <p>
     * 扫描指定类中的所有声明方法，筛选出带有 @IndicatorFunction 注解的方法， 并按函数名称进行分组，以支持方法重载
     * </p>
     *
     * @param clazz 要扫描的目标类
     * @return 按函数名称分组的方法映射
     */
    public Map<String, List<Method>> scanAnnotatedMethods(Class<?> clazz) {
        log.debug("开始扫描类 {} 中的注解方法", clazz.getSimpleName());

        Map<String, List<Method>> methodGroups = Arrays.stream(clazz.getDeclaredMethods())
            .filter(this::hasIndicatorFunctionAnnotation)
            .collect(Collectors.groupingBy(this::extractFunctionName));

        log.debug("扫描完成，发现 {} 个不同的函数名，共 {} 个方法",
            methodGroups.size(),
            methodGroups.values().stream().mapToInt(List::size).sum());

        return methodGroups;
    }

    /**
     * 检查方法是否有 @IndicatorFunction 注解
     *
     * @param method 要检查的方法
     * @return 如果有注解返回 true，否则返回 false
     */
    private boolean hasIndicatorFunctionAnnotation(Method method) {
        return method.getAnnotation(IndicatorFunction.class) != null;
    }

    /**
     * 从方法的 @IndicatorFunction 注解中提取函数名称
     *
     * @param method 方法对象
     * @return 函数名称
     */
    private String extractFunctionName(Method method) {
        IndicatorFunction annotation = method.getAnnotation(IndicatorFunction.class);
        if (annotation == null) {
            throw new IllegalArgumentException("方法 " + method.getName() + " 缺少 @IndicatorFunction 注解");
        }
        return annotation.enName();
    }
}
