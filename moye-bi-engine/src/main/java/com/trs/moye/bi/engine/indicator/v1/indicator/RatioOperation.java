package com.trs.moye.bi.engine.indicator.v1.indicator;

import com.trs.moye.base.data.indicator.enums.IndicatorStatOperation;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.springframework.stereotype.Component;

/**
 * 占比操作 基础代码实现
 */
@Component
public class RatioOperation extends AbstractStatOperation {

    @Override
    public void calculate(List<Map<String, Object>> currentData,
        List<Map<String, Object>> historyData,
        IndicatorStatParams params) {
        String targetField = getTargetField(params);
        String resultField = getResultField(params);
        if (Objects.isNull(targetField) || Objects.isNull(resultField)) {
            return;
        }
        if (useHistory(params) && Objects.nonNull(historyData) && !historyData.isEmpty()) {
            calculateRatio(historyData, targetField, resultField);
            copyHistoryResultToCurrent(currentData, historyData, params);
        } else {
            calculateRatio(currentData, targetField, resultField);
        }
    }

    /**
     * 计算占比
     *
     * @param dataList    数据列表
     * @param targetField 目标字段
     * @param resultField 结果字段
     */
    private void calculateRatio(List<Map<String, Object>> dataList, String targetField, String resultField) {
        // 计算总和（分母）
        double total = 0.0;
        for (Map<String, Object> data : dataList) {
            Number value = safeGetNumber(data, targetField);
            total += value.doubleValue();
        }

        // 防止除以零
        if (total == 0.0) {
            // 如果总和为零，所有占比都设为"0.00%"
            for (Map<String, Object> data : dataList) {
                data.put(resultField, "0.00%");
            }
            return;
        }

        // 计算每条记录的占比
        for (Map<String, Object> data : dataList) {
            Number value = safeGetNumber(data, targetField);
            String percentageStr = formatPercentage(value.doubleValue(), total);
            data.put(resultField, percentageStr);
        }
    }

    /**
     * 格式化百分比
     *
     * @param value 当前值
     * @param total 总值
     * @return 格式化后的百分比字符串
     */
    private String formatPercentage(double value, double total) {
        if (total == 0) {
            return "NaN";
        }
        double percentage = (value / total) * 100;
        return String.format("%.2f%%", percentage);
    }

    @Override
    public boolean isSupport(IndicatorStatOperation operation) {
        return IndicatorStatOperation.RATIO.equals(operation);
    }
}
