package com.trs.moye.bi.engine.indicator.function;

import com.trs.moye.base.data.indicator.entity.IndicatorApplicationFieldConfig;
import com.trs.moye.base.data.service.enums.EvaluateOperator;
import com.trs.moye.bi.engine.indicator.context.ParameterContext;
import com.trs.moye.bi.engine.indicator.entity.CalculationFunctionDefinition;
import com.trs.moye.bi.engine.indicator.entity.CalculationParameter;
import com.trs.moye.bi.engine.indicator.exception.IndicatorQueryException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 参数解析器
 * <p>
 * 负责将配置中的参数解析为类型安全的参数上下文 支持两种输入方式：传统的 List&lt;String&gt; 和新的 Map&lt;String, String&gt;
 * </p>
 *
 * <AUTHOR>
 * @since 2025/07/29
 */
@Slf4j
public class ParameterResolver {

    private ParameterResolver() {
    }

    /**
     * 解析参数配置
     * <p>
     * 根据函数定义和配置信息，解析出类型安全的参数上下文 优先使用命名参数，如果不存在则回退到传统列表参数
     * </p>
     *
     * @param functionDefinition 函数定义，包含参数结构
     * @param config             配置信息
     * @return 解析后的参数上下文
     * @throws IndicatorQueryException 当参数解析失败时抛出
     */
    public static ParameterContext resolve(CalculationFunctionDefinition functionDefinition,
        IndicatorApplicationFieldConfig config) {
        try {
            log.debug("开始解析参数, 函数: {}", functionDefinition.getFunctionName());

            ParameterContext context = new ParameterContext();

            // 添加原始配置信息用于调试
            context.addRawConfig("function", functionDefinition.getFunctionName());
            context.addRawConfig("input", config.getInput());
            context.addRawConfig("namedParameters", config.getNamedParameters());

            // 优先使用命名参数
            if (MapUtils.isNotEmpty(config.getNamedParameters())) {
                resolveFromNamedParameters(functionDefinition, config.getNamedParameters(), context);
            } else if (CollectionUtils.isNotEmpty(config.getInput())) {
                // 回退到传统列表参数
                resolveFromLegacyParameters(functionDefinition, config.getInput(), context);
            }

            // 验证必需参数
            validateRequiredParameters(functionDefinition, context);

            log.debug("参数解析完成, 函数: {}, 参数数量: {}",
                functionDefinition.getFunctionName(), context.getParameterCount());
            return context;

        } catch (Exception e) {
            log.error("参数解析失败, 函数: {}", functionDefinition.getFunctionName(), e);
            throw IndicatorQueryException.calculationError(functionDefinition.getFunctionName() + "参数解析失败", e);
        }
    }

    /**
     * 从命名参数中解析
     *
     * @param functionDefinition 函数定义
     * @param namedParameters    命名参数映射
     * @param context            参数上下文
     */
    private static void resolveFromNamedParameters(CalculationFunctionDefinition functionDefinition,
        Map<String, String> namedParameters,
        ParameterContext context) {
        log.debug("使用命名参数解析");

        for (CalculationParameter parameter : functionDefinition.getParameters()) {
            String paramName = parameter.getName();
            String rawValue = namedParameters.get(paramName);

            if (rawValue != null) {
                // 解析并转换参数值
                Object convertedValue = convertParameterValue(parameter, rawValue);
                context.addParameter(paramName, convertedValue);
            } else if (!parameter.isRequired() && parameter.getDefaultValue() != null) {
                // 使用默认值
                context.addParameter(paramName, parameter.getDefaultValue());
            }
        }
    }

    /**
     * 从传统列表参数中解析
     * <p>
     * 处理复杂的传统参数映射场景：
     * <ol>
     *   <li>verticalSum: 参数顺序为 [targetField, ...dimensionFields, newFieldName, newFieldValue]</li>
     *   <li>verticalSumEx: 根据最后一个参数是否为布尔值来决定调用哪个重载版本</li>
     *   <li>dailyAverage: 根据参数数量和类型（整数/字符串）智能选择重载方法</li>
     *   <li>其他函数: 按照标准的 legacyIndex 映射</li>
     * </ol>
     * </p>
     *
     * @param functionDefinition 函数定义
     * @param legacyParameters   传统列表参数
     * @param context            参数上下文
     */
    private static void resolveFromLegacyParameters(CalculationFunctionDefinition functionDefinition,
        List<String> legacyParameters,
        ParameterContext context) {
        log.debug("使用传统列表参数解析，函数: {}, 参数数量: {}",
            functionDefinition.getFunctionName(), legacyParameters.size());

        String functionName = functionDefinition.getFunctionName();

        // 处理特殊的复杂参数映射场景
        if ("verticalSum".equals(functionName)) {
            resolveVerticalSumParameters(functionDefinition, legacyParameters, context);
        } else if ("verticalSumEx".equals(functionName)) {
            resolveVerticalSumExParameters(functionDefinition, legacyParameters, context);
        } else if ("dailyAverage".equals(functionName)) {
            resolveDailyAverageParameters(functionDefinition, legacyParameters, context);
        } else {
            // 标准的参数映射处理
            resolveStandardLegacyParameters(functionDefinition, legacyParameters, context);
        }
    }

    /**
     * 转换参数值
     *
     * @param parameter 参数定义
     * @param rawValue  原始字符串值
     * @return 转换后的参数值
     */
    private static Object convertParameterValue(CalculationParameter parameter, String rawValue) {
        if (StringUtils.isBlank(rawValue)) {
            return parameter.getDefaultValue();
        }

        Class<?> targetType = parameter.getType();

        try {
            // 字符串类型直接返回
            if (targetType == String.class) {
                return rawValue;
            }

            // 整数类型
            if (targetType == Integer.class || targetType == int.class) {
                return Integer.valueOf(rawValue);
            }

            // 布尔类型
            if (targetType == Boolean.class || targetType == boolean.class) {
                return Boolean.parseBoolean(rawValue) || "1".equals(rawValue) || "yes".equalsIgnoreCase(rawValue);
            }

            // 枚举类型
            if (targetType.isEnum()) {
                @SuppressWarnings("unchecked")
                Class<? extends Enum<?>> enumClass = (Class<? extends Enum<?>>) targetType;
                return Enum.valueOf((Class) enumClass, rawValue);
            }

            // EvaluateOperator 特殊处理
            if (targetType == EvaluateOperator.class) {
                return EvaluateOperator.valueOf(rawValue);
            }

            // 其他类型暂时返回字符串
            log.warn("未知参数类型 {}, 返回字符串值", targetType.getSimpleName());
            return rawValue;

        } catch (Exception e) {
            log.warn("参数值转换失败, 参数: {}, 值: {}, 目标类型: {}",
                parameter.getName(), rawValue, targetType.getSimpleName(), e);
            return parameter.getDefaultValue();
        }
    }

    /**
     * 验证必需参数
     *
     * @param functionDefinition 函数定义
     * @param context            参数上下文
     * @throws IndicatorQueryException 当必需参数缺失时抛出
     */
    private static void validateRequiredParameters(CalculationFunctionDefinition functionDefinition,
        ParameterContext context) {
        for (CalculationParameter parameter : functionDefinition.getParameters()) {
            if (parameter.isRequired() && !context.hasParameter(parameter.getName())) {
                String message = String.format("函数 %s 缺少必需参数: %s (%s)",
                    functionDefinition.getFunctionName(),
                    parameter.getName(),
                    parameter.getDescription());
                throw IndicatorQueryException.validationError(message);
            }
        }
    }

    /**
     * 处理 verticalSum 函数的复杂参数映射
     * <p>
     * 参数结构：[targetField, ...dimensionFields, newFieldName, newFieldValue] 其中 dimensionFields 是可变长度的字段列表
     * <p>
     * <b>参数映射逻辑：</b>
     * <ol>
     * <li>第1个参数：targetField（要求和的目标字段）</li>
     * <li>中间参数：dimensionFields（用于分组的维度字段列表，可以为空）</li>
     * <li>倒数第2个参数：newFieldName（新合计行中用于标识的字段名）</li>
     * <li>最后1个参数：newFieldValue（新合计行中标识字段的值）</li>
     * </ol>
     * <p>
     * <b>边界情况处理：</b>
     * <ul>
     * <li>最少3个参数：[targetField, newFieldName, newFieldValue]（无维度字段）</li>
     * <li>参数不足时抛出异常，确保调用方能够感知错误</li>
     * </ul>
     * </p>
     *
     * @param definition       定义
     * @param legacyParameters 传统列表参数
     * @param context          参数上下文
     */
    private static void resolveVerticalSumParameters(CalculationFunctionDefinition definition,
        List<String> legacyParameters, ParameterContext context) {
        if (legacyParameters.size() < 3) {
            String errorMsg = String.format("verticalSum 参数数量不足，至少需要3个参数，实际提供了%d个参数: %s",
                legacyParameters.size(), legacyParameters);
            log.error(errorMsg);
            throw new IllegalArgumentException(errorMsg);
        }

        // 从函数定义中获取参数名称映射，避免硬编码
        Map<Integer, String> legacyIndexToName = definition.getParameters().stream()
            .collect(Collectors.toMap(
                CalculationParameter::getLegacyIndex,
                CalculationParameter::getName
            ));

        // 验证必需的参数索引是否存在
        if (!legacyIndexToName.containsKey(0) || !legacyIndexToName.containsKey(1)
            || !legacyIndexToName.containsKey(2) || !legacyIndexToName.containsKey(3)) {
            throw new IllegalArgumentException("verticalSum 函数定义中缺少必需的参数索引");
        }

        // 第一个参数：targetField
        String targetFieldName = legacyIndexToName.get(0);  // 第一个参数名称
        String targetField = legacyParameters.get(0);
        context.addParameter(targetFieldName, targetField);
        // 最后两个参数：newFieldName 和 newFieldValue
        int lastIndex = legacyParameters.size() - 1;
        String newFieldName = legacyParameters.get(lastIndex - 1);
        String newFieldValue = legacyParameters.get(lastIndex);
        String newFieldNameParam = legacyIndexToName.get(2);  // 新字段名参数名称
        String newFieldValueParam = legacyIndexToName.get(3);  // 新字段值参数名称
        context.addParameter(newFieldNameParam, newFieldName);
        context.addParameter(newFieldValueParam, newFieldValue);

        // 中间的参数：dimensionFields（可能为空列表）
        List<String> dimensionFields;
        if (lastIndex - 1 > 1) {
            dimensionFields = legacyParameters.subList(1, lastIndex - 1);
        } else {
            dimensionFields = new ArrayList<>(); // 无维度字段的情况
        }
        String dimensionFieldsName = legacyIndexToName.get(1);  // 维度字段列表参数名称
        context.addParameter(dimensionFieldsName, dimensionFields);

        log.debug("verticalSum 参数解析完成: {}={}, {}={} ({}个), {}={}, {}={}",
            targetFieldName, targetField, dimensionFieldsName, dimensionFields, dimensionFields.size(),
            newFieldNameParam, newFieldName, newFieldValueParam, newFieldValue);
    }

    /**
     * 处理 verticalSumEx 函数的复杂参数映射
     * <p>
     * 根据最后一个参数是否为布尔值来智能决定参数结构：
     * <ul>
     * <li>四参数版本：[targetField, ...dimensionFields, newFieldsAndValuesJson, summarizeSingleRowGroups]</li>
     * <li>三参数版本：[targetField, ...dimensionFields, newFieldsAndValuesJson]</li>
     * </ul>
     * <p>
     * <b>布尔值判断逻辑：</b>
     * <ul>
     * <li>最后一个参数为 "true" 或 "false"（忽略大小写）时，识别为四参数版本</li>
     * <li>其他情况均识别为三参数版本</li>
     * </ul>
     * <p>
     * <b>参数映射说明：</b>
     * <ol>
     * <li>第1个参数：targetField（要求和的目标字段）</li>
     * <li>中间参数：dimensionFields（用于分组的维度字段列表）</li>
     * <li>倒数第2个或最后1个参数：newFieldsAndValuesJson（JSON格式的字段映射配置）</li>
     * <li>最后1个参数（仅四参数版本）：summarizeSingleRowGroups（是否为单行分组生成合计行）</li>
     * </ol>
     * </p>
     *
     * @param definition       定义
     * @param legacyParameters 传统列表参数
     * @param context          参数上下文
     */
    private static void resolveVerticalSumExParameters(CalculationFunctionDefinition definition,
        List<String> legacyParameters, ParameterContext context) {
        if (legacyParameters.size() < 3) {
            String errorMsg = String.format("verticalSumEx 参数数量不足，至少需要3个参数，实际提供了%d个参数: %s",
                legacyParameters.size(), legacyParameters);
            log.error(errorMsg);
            throw new IllegalArgumentException(errorMsg);
        }

        // 从函数定义中获取参数名称映射，避免硬编码
        Map<Integer, String> legacyIndexToName = definition.getParameters().stream()
            .collect(Collectors.toMap(
                CalculationParameter::getLegacyIndex,
                CalculationParameter::getName
            ));

        // 验证必需的参数索引是否存在
        if (!legacyIndexToName.containsKey(0) || !legacyIndexToName.containsKey(1)
            || !legacyIndexToName.containsKey(2)) {
            throw new IllegalArgumentException("verticalSumEx 函数定义中缺少必需的参数索引");
        }

        // 动态获取参数名称
        String targetFieldName = legacyIndexToName.get(0);  // 第一个参数名称
        String dimensionFieldsName = legacyIndexToName.get(1);  // 维度字段列表参数名称
        String newFieldsAndValuesName = legacyIndexToName.get(2);  // JSON配置参数名称
        String summarizeSingleRowGroupsName = legacyIndexToName.get(3);  // 布尔参数名称（可能为null）

        // 第一个参数：targetField
        String targetField = legacyParameters.get(0);
        context.addParameter(targetFieldName, targetField);

        // 检查最后一个参数是否为布尔值
        String lastParam = legacyParameters.get(legacyParameters.size() - 1);
        boolean hasBoolean = "true".equalsIgnoreCase(lastParam) || "false".equalsIgnoreCase(lastParam);

        if (hasBoolean && legacyParameters.size() >= 4 && summarizeSingleRowGroupsName != null) {
            // 四参数版本：[targetField, ...dimensionFields, newFieldsAndValues, summarizeSingleRowGroups]
            int jsonIndex = legacyParameters.size() - 2;
            String newFieldsAndValuesJson = legacyParameters.get(jsonIndex);
            Boolean summarizeSingleRowGroups = Boolean.valueOf(lastParam);

            context.addParameter(newFieldsAndValuesName, newFieldsAndValuesJson);
            context.addParameter(summarizeSingleRowGroupsName, summarizeSingleRowGroups);

            // 中间的参数是 dimensionFields
            List<String> dimensionFields;
            dimensionFields = legacyParameters.subList(1, jsonIndex);
            context.addParameter(dimensionFieldsName, dimensionFields);

            log.debug("verticalSumEx 四参数版本解析完成: {}={}, {}={} ({}个), {}={}",
                targetFieldName, targetField, dimensionFieldsName, dimensionFields,
                dimensionFields.size(), summarizeSingleRowGroupsName, summarizeSingleRowGroups);
        } else {
            // 三参数版本：[targetField, ...dimensionFields, newFieldsAndValues]
            int jsonIndex = legacyParameters.size() - 1;
            String newFieldsAndValuesJson = legacyParameters.get(jsonIndex);

            context.addParameter(newFieldsAndValuesName, newFieldsAndValuesJson);

            // 中间的参数是 dimensionFields
            List<String> dimensionFields;
            if (jsonIndex > 1) {
                dimensionFields = legacyParameters.subList(1, jsonIndex);
            } else {
                dimensionFields = new ArrayList<>();
            }
            context.addParameter(dimensionFieldsName, dimensionFields);

            log.debug("verticalSumEx 三参数版本解析完成: {}={}, {}={} ({}个), lastParam={}",
                targetFieldName, targetField, dimensionFieldsName, dimensionFields,
                dimensionFields.size(), lastParam);
        }
    }

    /**
     * 处理 dailyAverage 函数的复杂参数映射
     * <p>
     * 根据参数数量和类型智能选择重载方法：
     * <ul>
     * <li>1个参数：[targetField] - 使用默认精度和周期类型</li>
     * <li>2个参数：[targetField, precision] 或 [targetField, periodType] - 智能类型推断</li>
     * <li>3个参数：[targetField, periodType, precision] - 完整参数版本</li>
     * </ul>
     * <p>
     * <b>类型推断逻辑：</b>
     * <ul>
     * <li>第2个参数能解析为整数时，识别为精度参数</li>
     * <li>第2个参数不能解析为整数时，识别为周期类型参数</li>
     * <li>第3个参数必须能解析为整数，否则抛出异常</li>
     * </ul>
     * <p>
     * <b>参数说明：</b>
     * <ol>
     * <li>targetField：目标字段名称（必需）</li>
     * <li>periodType：周期类型，如 "MONTH"、"YEAR"、"LAST_YEAR" 等（可选）</li>
     * <li>precision：计算精度，即小数位数（可选，默认为2）</li>
     * </ol>
     * </p>
     *
     * @param definition       定义
     * @param legacyParameters 传统列表参数
     * @param context          参数上下文
     */
    private static void resolveDailyAverageParameters(CalculationFunctionDefinition definition,
        List<String> legacyParameters, ParameterContext context) {
        if (legacyParameters.isEmpty()) {
            String errorMsg = "dailyAverage 参数数量不足，至少需要1个参数（targetField）";
            log.error(errorMsg);
            throw new IllegalArgumentException(errorMsg);
        }

        // 从函数定义中获取参数名称映射，避免硬编码
        Map<Integer, String> legacyIndexToName = definition.getParameters().stream()
            .collect(Collectors.toMap(
                CalculationParameter::getLegacyIndex,
                CalculationParameter::getName
            ));

        // 验证必需的参数索引是否存在
        if (!legacyIndexToName.containsKey(0)) {
            throw new IllegalArgumentException("dailyAverage 函数定义中缺少必需的参数索引 0");
        }

        // 动态获取参数名称
        String targetFieldName = legacyIndexToName.get(0);  // 第一个参数名称
        String periodTypeName = legacyIndexToName.get(1);   // 周期类型参数名称（可能为null）
        String precisionName = legacyIndexToName.get(2);    // 精度参数名称（可能为null）

        // 第一个参数总是 targetField
        String targetField = legacyParameters.get(0);
        context.addParameter(targetFieldName, targetField);

        if (legacyParameters.size() == 2) {
            // 两个参数：需要判断第二个参数是精度（整数）还是周期类型（字符串）
            String secondParam = legacyParameters.get(1);
            try {
                // 尝试解析为整数
                Integer precision = Integer.valueOf(secondParam);
                if (precisionName != null) {
                    context.addParameter(precisionName, precision);
                    log.debug("dailyAverage 两参数版本（精度）解析完成: {}={}, {}={}",
                        targetFieldName, targetField, precisionName, precision);
                }
            } catch (NumberFormatException e) {
                // 解析失败，假定为周期类型
                if (periodTypeName != null) {
                    context.addParameter(periodTypeName, secondParam);
                    log.debug("dailyAverage 两参数版本（周期类型）解析完成: {}={}, {}={}",
                        targetFieldName, targetField, periodTypeName, secondParam);
                }
            }
        } else if (legacyParameters.size() == 3) {
            // 三个参数：[targetField, periodType, precision]
            String periodType = legacyParameters.get(1);
            String precisionStr = legacyParameters.get(2);

            try {
                Integer precision = Integer.valueOf(precisionStr);
                if (periodTypeName != null) {
                    context.addParameter(periodTypeName, periodType);
                }
                if (precisionName != null) {
                    context.addParameter(precisionName, precision);
                }
                log.debug("dailyAverage 三参数版本解析完成: {}={}, {}={}, {}={}",
                    targetFieldName, targetField, periodTypeName, periodType, precisionName, precision);
            } catch (NumberFormatException e) {
                String errorMsg = String.format("dailyAverage 第3个参数必须是有效的整数（精度），实际值: %s",
                    precisionStr);
                log.error(errorMsg);
                throw new IllegalArgumentException(errorMsg, e);
            }
        } else if (legacyParameters.size() > 3) {
            log.warn("dailyAverage 参数过多，只使用前3个参数，忽略多余参数: {}",
                legacyParameters.subList(3, legacyParameters.size()));
            // 递归调用处理前3个参数
            resolveDailyAverageParameters(definition, legacyParameters.subList(0, 3), context);
        } else {
            // 1个参数的情况，只设置 targetField，其他参数使用默认值
            log.debug("dailyAverage 单参数版本解析完成: {}={}", targetFieldName, targetField);
        }
    }

    /**
     * 标准的传统参数映射处理
     * <p>
     * 处理标准的参数映射场景，特别针对算术运算函数的 List 类型参数进行优化。 对于 List 类型参数（如算术运算函数的 fields 参数），将整个 legacyParameters 列表作为参数值，而不是单个元素。
     * </p>
     *
     * @param functionDefinition 函数定义
     * @param legacyParameters   传统列表参数
     * @param context            参数上下文
     */
    private static void resolveStandardLegacyParameters(CalculationFunctionDefinition functionDefinition,
        List<String> legacyParameters,
        ParameterContext context) {

        for (CalculationParameter parameter : functionDefinition.getParameters()) {
            int legacyIndex = parameter.getLegacyIndex();
            String paramName = parameter.getName();
            Class<?> paramType = parameter.getType();

            // 特殊处理 List 类型参数（主要用于算术运算函数）
            if (paramType == List.class && legacyIndex == 0) {
                // 对于 List 类型参数，使用整个 legacyParameters 列表
                if (!legacyParameters.isEmpty()) {
                    context.addParameter(paramName, new ArrayList<>(legacyParameters));
                    log.debug("List 类型参数 {} 解析完成，包含 {} 个元素: {}",
                        paramName, legacyParameters.size(), legacyParameters);
                } else if (!parameter.isRequired() && parameter.getDefaultValue() != null) {
                    context.addParameter(paramName, parameter.getDefaultValue());
                }
            } else {
                // 标准的单值参数处理
                if (legacyIndex >= 0 && legacyIndex < legacyParameters.size()) {
                    String rawValue = legacyParameters.get(legacyIndex);
                    if (StringUtils.isNotBlank(rawValue)) {
                        Object convertedValue = convertParameterValue(parameter, rawValue);
                        context.addParameter(paramName, convertedValue);
                    }
                } else if (!parameter.isRequired() && parameter.getDefaultValue() != null) {
                    // 使用默认值
                    context.addParameter(paramName, parameter.getDefaultValue());
                }
            }
        }
    }
}
