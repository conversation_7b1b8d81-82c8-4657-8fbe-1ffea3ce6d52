package com.trs.moye.bi.engine.indicator.exception;

import com.trs.moye.base.common.exception.BizException;
import lombok.Getter;

/**
 * 指标查询专用异常类.
 * <p>
 * 提供更具体的异常信息和错误分类
 *
 * <AUTHOR>
 * @since 2025/07/23
 */
@Getter
public class IndicatorQueryException extends BizException {

    /**
     * 异常类型枚举
     */
    @Getter
    public enum ErrorType {
        /**
         * 数据连接不存在.
         */
        CONNECTION_NOT_FOUND("数据连接不存在"),
        /**
         * 指标配置不存在.
         */
        INDICATOR_CONFIG_NOT_FOUND("指标配置不存在"),
        /**
         * 字段配置错误.
         */
        FIELD_CONFIG_ERROR("字段配置错误"),
        /**
         * 数据查询失败.
         */
        DATA_QUERY_ERROR("数据查询失败"),
        /**
         * 指标计算失败.
         */
        CALCULATION_ERROR("指标计算失败"),
        /**
         * 数据处理失败.
         */
        DATA_PROCESSING_ERROR("数据处理失败"),
        /**
         * 数据分组失败.
         */
        GROUPING_ERROR("数据分组失败"),
        /**
         * 参数验证失败.
         */
        VALIDATION_ERROR("参数验证失败");

        /**
         * 错误描述
         */
        private final String description;

        /**
         * 构造函数.
         *
         * @param description 错误描述
         */
        ErrorType(final String description) {
            this.description = description;
        }

    }

    /**
     * 错误类型
     */
    private final ErrorType errorType;
    /**
     * 详细信息
     */
    private final String detail;

    /**
     * 构造函数
     *
     * @param errorType 错误类型
     * @param detail    详细信息
     */
    public IndicatorQueryException(final ErrorType errorType, final String detail) {
        super(errorType.getDescription() + ": " + detail);
        this.errorType = errorType;
        this.detail = detail;
    }

    /**
     * 构造函数
     *
     * @param errorType 错误类型
     * @param detail    详细信息
     * @param cause     原因异常
     */
    public IndicatorQueryException(final ErrorType errorType, final String detail,
        final Throwable cause) {
        super(errorType.getDescription() + ": " + detail, cause);
        this.errorType = errorType;
        this.detail = detail;
    }

    /**
     * 构造函数
     *
     * @param errorType 错误类型
     * @param cause     原因异常
     */
    public IndicatorQueryException(final ErrorType errorType, final Throwable cause) {
        super(errorType.getDescription(), cause);
        this.errorType = errorType;
        this.detail = null;
    }

    /**
     * 创建连接不存在异常
     *
     * @param connectionId 连接ID
     * @return 异常实例
     */
    public static IndicatorQueryException connectionNotFound(final Integer connectionId) {
        return new IndicatorQueryException(ErrorType.CONNECTION_NOT_FOUND,
            "连接ID: " + connectionId);
    }

    /**
     * 创建配置不存在异常
     *
     * @param dataModelId 数据模型ID
     * @return 异常实例
     */
    public static IndicatorQueryException configNotFound(final Integer dataModelId) {
        return new IndicatorQueryException(ErrorType.INDICATOR_CONFIG_NOT_FOUND,
            "数据模型ID: " + dataModelId);
    }

    /**
     * 创建数据查询异常
     *
     * @param sql   SQL语句
     * @param cause 原因异常
     * @return 异常实例
     */
    public static IndicatorQueryException dataQueryError(final String sql,
        final Throwable cause) {
        return new IndicatorQueryException(ErrorType.DATA_QUERY_ERROR,
            "SQL: " + sql, cause);
    }

    /**
     * 创建计算异常
     *
     * @param functionName 函数名称
     * @param cause        原因异常
     * @return 异常实例
     */
    public static IndicatorQueryException calculationError(final String functionName,
        final Throwable cause) {
        return new IndicatorQueryException(ErrorType.CALCULATION_ERROR,
            "函数: " + functionName, cause);
    }

    /**
     * 创建数据处理异常
     *
     * @param operation 操作名称
     * @param cause     原因异常
     * @return 异常实例
     */
    public static IndicatorQueryException dataProcessingError(final String operation,
        final Throwable cause) {
        return new IndicatorQueryException(ErrorType.DATA_PROCESSING_ERROR,
            "操作: " + operation, cause);
    }

    /**
     * 创建分组异常
     *
     * @param detail 详细信息
     * @param cause  原因异常
     * @return 异常实例
     */
    public static IndicatorQueryException groupingError(final String detail,
        final Throwable cause) {
        return new IndicatorQueryException(ErrorType.GROUPING_ERROR, detail, cause);
    }

    /**
     * 创建验证异常
     *
     * @param detail 详细信息
     * @return 异常实例
     */
    public static IndicatorQueryException validationError(final String detail) {
        return new IndicatorQueryException(ErrorType.VALIDATION_ERROR, detail);
    }
}
