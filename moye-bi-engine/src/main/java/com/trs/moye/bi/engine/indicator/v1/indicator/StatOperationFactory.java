package com.trs.moye.bi.engine.indicator.v1.indicator;

import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * 工厂类，根据操作类型返回对应的 StatOperation 实例
 *
 * <AUTHOR>
 * @since 2025/05/27
 */
@Component
public class StatOperationFactory {

    @Resource
    private List<StatOperation> operations;


    /**
     * 进行统计计算
     *
     * @param currentData         当前数据
     * @param historyData         历史数据
     * @param indicatorStatParams 指标统计参数
     * <AUTHOR>
     * @since 2025/05/28 11:15:01
     */
    public void doCalculate(List<Map<String, Object>> currentData, List<Map<String, Object>> historyData,
        IndicatorStatParams indicatorStatParams) {
        for (StatOperation processor : operations) {
            if (processor.isSupport(indicatorStatParams.getFieldConfig().getOperation())) {
                processor.calculate(currentData, historyData, indicatorStatParams);
            }
        }
    }
}
